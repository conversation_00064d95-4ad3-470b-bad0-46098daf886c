'use server';

import { request } from '@/apis/request';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';

// Define interfaces for API data structures
interface ISchool {
  id: string;
  name: string;
}

interface ExtractNarrativeStructureResponseDto {
  id: string;
  schoolId: string;
  status: NarrativeStructureStatus;
  createdAt: string;
}

interface NarrativeStructureResponseDto {
  id: string;
  schoolId: string;
  content: string;
  status: NarrativeStructureStatus;
  createdAt: string;
  updatedAt: string;
  extractedBy?: string;
}

enum NarrativeStructureStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

/**
 * Extracts a narrative structure for a school
 * @param schoolId - The ID of the school
 * @returns The extracted narrative structure status
 */
export async function extractNarrativeStructureAction(
  schoolId: string
): Promise<TTransformResponse<ExtractNarrativeStructureResponseDto>> {
  try {
    const response = await request<ExtractNarrativeStructureResponseDto>({
      url: `/schools/${schoolId}/narrative-structure/extract`,
      options: {
        method: 'POST',
      },
    });

    if (response.status === 'success') {
      // Revalidate the school detail page
      revalidatePath(`/school-management/${schoolId}`);
    }

    return response;
  } catch (error: any) {
    console.error('Error extracting narrative structure:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while extracting the narrative structure.' 
    };
  }
}

/**
 * Gets the narrative structure for a school
 * @param schoolId - The ID of the school
 * @returns The narrative structure
 */
export async function getNarrativeStructureAction(
  schoolId: string
): Promise<TTransformResponse<NarrativeStructureResponseDto>> {
  try {
    // Get the session to extract the accessToken
    const session = await getServerSession(authOptions);
    const accessToken = session?.user?.accessToken;
    
    if (!accessToken) {
      return {
        status: 'error',
        message: 'Authentication token is missing. Please log in again.'
      };
    }

    const response = await request<NarrativeStructureResponseDto>({
      url: `/schools/${schoolId}/narrative-structure`,
      options: {
        method: 'GET',
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error fetching narrative structure:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while fetching the narrative structure.' 
    };
  }
}

/**
 * Deletes the narrative structure for a school
 * @param schoolId - The ID of the school
 * @returns Success or error status
 */
export async function deleteNarrativeStructureAction(
  schoolId: string
): Promise<TTransformResponse<any>> {
  try {
    const response = await request<any>({
      url: `/schools/${schoolId}/narrative-structure`,
      options: {
        method: 'DELETE',
      },
    });

    if (response.status === 'success') {
      // Revalidate the school detail page
      revalidatePath(`/school-management/${schoolId}`);
    }

    return response;
  } catch (error: any) {
    console.error('Error deleting narrative structure:', error);
    return { 
      status: 'error', 
      message: error.message || 'An unexpected error occurred while deleting the narrative structure.' 
    };
  }
}