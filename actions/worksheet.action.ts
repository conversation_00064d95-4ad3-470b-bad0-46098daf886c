'use server';

import {onCreateWorksheet, TCreateWorksheetReq, TWorksheet, getWorksheets as apiGetWorksheets, deleteWorksheetById } from "@/apis/worksheet"; // Renamed imported getWorksheets
import {ERoutes} from "@/config/enums/enum";
import {redirect} from 'next/navigation';
import { revalidatePath } from 'next/cache';
import { TPagination, TPaginationReq } from "@/@types/pagination";
import { TTransformResponse } from "@/apis/transformResponse"; // To ensure serializable return type

export async function handleGenerateWorksheet(
  data: FormData
) {

  const response = await onCreateWorksheet(data)
  if (response.status === 'error') {
    return {message:'Failed to create worksheet', status: 'error'}
  }

  revalidatePath(ERoutes.MANAGE_WORKSHEET)
  redirect(ERoutes.MANAGE_WORKSHEET)

}

// New Server Action for fetching worksheets
export async function getWorksheetsAction(
  params: TPaginationReq
): Promise<TTransformResponse<TPagination<TWorksheet>>> {
  // Now that `apiGetWorksheets` (which is `getWorksheets` from `apis/worksheet.ts`)
  // has been reverted to call the backend directly using the main `request` function,
  // this server action can simply call it.
  return apiGetWorksheets(params);
}

export async function handleDeleteWorksheetAction(
  worksheetId: string
): Promise<{ status: 'success' | 'error'; message?: string }> {
  try {
    const response = await deleteWorksheetById(worksheetId); // Assuming deleteWorksheetById exists in apis/worksheet.ts
    if (response.status === 'success') {
      revalidatePath(ERoutes.MANAGE_WORKSHEET); // Revalidate the page to reflect changes
      return { status: 'success', message: response.data?.message || 'Worksheet deleted successfully.' };
    } else {
      // For TError, message is directly on response or response.error.message
      // Assuming TError has a 'message' property directly or via 'error' object
      const errorMessage = response.message || (response as any).error?.message || 'Failed to delete worksheet.';
      return { status: 'error', message: Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage };
    }
  } catch (error: any) {
    console.error('Error deleting worksheet:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred.' };
  }
}
