'use server';

import { request } from '@/apis/request';
import { TTransformResponse } from '@/apis/transformResponse';
import { EAPIEndpoint } from '@/@types/enums/api';
import { createFileRenderUrl } from '@/utils/fileUtils';

/**
 * Upload a single file to the server
 * @param file The file to upload
 * @param description Optional description for the file
 * @param category Optional category for the file
 * @param tags Optional tags for the file
 * @returns Promise with the upload response
 */
export async function handleFileUploadAction(
  file: File,
  description?: string,
  category?: string,
  tags?: string[]
): Promise<TTransformResponse<{ url: string; fileId: string }>> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    if (description) {
      formData.append('description', description);
    }

    if (category) {
      formData.append('category', category);
    }

    if (tags && tags.length > 0) {
      tags.forEach(tag => {
        formData.append('tags', tag);
      });
    }

    // Use the request utility with noContentType set to true for FormData
    const response = await request<{ url: string; fileId: string }>({
      url: `${EAPIEndpoint.FILES}/upload`,
      options: {
        method: 'POST',
        body: formData,
      },
      noContentType: true, // Important for FormData to work correctly
    });

    // If successful, convert the response to use the new render URL format
    if (response.status === 'success' && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          url: createFileRenderUrl(response.data.fileId), // Use the new render URL format
        },
      };
    }

    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An error occurred while uploading the file',
    };
  }
}
