'use server';

import {
  createBrand,
  getAllBrands,
  updateBrand,
  ICreateBrandPayload,
  IUpdateBrandPayload,
  IBrandResponse
} from '@/apis/brandApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';

// Define specific paths for revalidation
const USERS_MANAGEMENT_PATH = '/users-management';
const USER_CREATE_PATH = '/users-management/create';

/**
 * Fetches all brands.
 * @returns A list of all brands.
 */
export async function handleGetAllBrandsAction(): Promise<TTransformResponse<IBrandResponse[]>> {
  try {
    const response = await getAllBrands();
    return response;
  } catch (error: any) {
    console.error('Error in handleGetAllBrandsAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Creates a new brand.
 * @param payload - The brand data.
 * @returns The created brand.
 */
export async function handleCreateBrandAction(
  payload: ICreateBrandPayload
): Promise<TTransformResponse<IBrandResponse>> {
  try {
    // Ensure optional fields are truly optional or pass undefined if empty string
    const cleanedPayload: ICreateBrandPayload = {
      logo: payload.logo || undefined,
      color: payload.color || undefined,
      image: payload.image || undefined,
    };

    const response = await createBrand(cleanedPayload);

    if (response.status === 'success') {
      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleCreateBrandAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Updates an existing brand.
 * @param brandId - The ID of the brand to update.
 * @param payload - The brand data to update.
 * @returns The updated brand.
 */
export async function handleUpdateBrandAction(
  brandId: string,
  payload: IUpdateBrandPayload
): Promise<TTransformResponse<IBrandResponse>> {
  try {
    // Ensure optional fields are truly optional or pass undefined if empty string
    const cleanedPayload: IUpdateBrandPayload = {
      logo: payload.logo || undefined,
      color: payload.color || undefined,
      image: payload.image || undefined,
    };

    const response = await updateBrand(brandId, cleanedPayload);

    if (response.status === 'success') {
      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleUpdateBrandAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}
