'use client';

import { useEffect, useState } from 'react';
import { getSocket } from '@/lib/socket';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';
import { accumulateQuestions } from '@/utils/questionUtils';

type WorksheetProgressData = {
  worksheetId: string;
  progress: ProgressData;
  questionResult: {
    result: Question[];
  };
  timestamp: string;
};

interface UseWorksheetProgressProps {
  worksheetId?: string;
  initialStatus?: WorksheetGeneratingStatus;
  initialQuestions?: Question[];
  initialProgress?: ProgressData | null;
}

interface UseWorksheetProgressResult {
  status: WorksheetGeneratingStatus;
  questions: Question[];
  progress: ProgressData | null;
  hasReceivedProgressUpdate: boolean;
}

/**
 * Custom hook to handle worksheet progress socket events
 */
export const useWorksheetProgress = ({
  worksheetId,
  initialStatus = WorksheetGeneratingStatus.PENDING,
  initialQuestions = [],
  initialProgress = null,
}: UseWorksheetProgressProps): UseWorksheetProgressResult => {
  const [status, setStatus] = useState<WorksheetGeneratingStatus>(initialStatus);
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);
  const [progress, setProgress] = useState<ProgressData | null>(initialProgress || null);
  const [hasReceivedProgressUpdate, setHasReceivedProgressUpdate] = useState(false);

  useEffect(() => {
    if (!worksheetId) return;

    const socket = getSocket();

    // Handle worksheet generation completion
    socket.on('worksheet:generated', (data: { worksheetId: string }) => {
      if (data.worksheetId === worksheetId) {
        setStatus(WorksheetGeneratingStatus.GENERATED);
      }
    });

    // Handle worksheet generation error
    socket.on('worksheet:error', (data: { worksheetId: string }) => {
      if (data.worksheetId === worksheetId) {
        setStatus(WorksheetGeneratingStatus.ERROR);
      }
    });

    // Handle worksheet generation progress
    socket.on('worksheet:progress', (data: WorksheetProgressData) => {
      if (data.worksheetId === worksheetId) {
        setHasReceivedProgressUpdate(true);

        // Update progress
        setProgress(data.progress);

        // Accumulate questions instead of replacing them
        if (data.questionResult && data.questionResult.result && data.questionResult.result.length > 0) {
          setQuestions(prevQuestions => accumulateQuestions(prevQuestions, data.questionResult.result));
        }
      }
    });

    // Handle global worksheet status updates from other views
    const handleWorksheetUpdate = (event: CustomEvent) => {
      const updateData = event.detail;
      if (updateData.result && updateData.result.id === worksheetId) {
        // Update status if available
        if (updateData.status) {
          if (updateData.status === 'generated') {
            setStatus(WorksheetGeneratingStatus.GENERATED);
          } else if (updateData.status === 'error') {
            setStatus(WorksheetGeneratingStatus.ERROR);
          } else if (updateData.status === 'pending') {
            setStatus(WorksheetGeneratingStatus.PENDING);
          }
        }

        // Update questions if available
        if (updateData.result.questions) {
          setQuestions(updateData.result.questions);
        }
      }
    };

    // Add event listener for the custom event
    window.addEventListener('worksheet_updated', handleWorksheetUpdate as EventListener);

    return () => {
      socket.off('worksheet:generated');
      socket.off('worksheet:error');
      socket.off('worksheet:progress');
      window.removeEventListener('worksheet_updated', handleWorksheetUpdate as EventListener);
    };
  }, [worksheetId]);

  return {
    status,
    questions,
    progress,
    hasReceivedProgressUpdate,
  };
};
