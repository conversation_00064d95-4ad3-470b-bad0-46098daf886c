'use client';

import { useSession } from 'next-auth/react';
import { IUserResponse } from '@/apis/userApi';

/**
 * Custom hook for updating the Next Auth session with user profile data
 * 
 * @returns An object containing:
 * - updateSessionUser: Function to update the session with user profile data
 * - session: The current session data
 * - status: The session status ('loading', 'authenticated', 'unauthenticated')
 */
export function useSessionUpdate() {
  const { data: session, update, status } = useSession();

  /**
   * Updates the Next Auth session with user profile data
   * 
   * @param userData - The updated user data from the API
   * @returns A promise that resolves when the session has been updated
   */
  const updateSessionUser = async (userData: Partial<IUserResponse>) => {
    if (!userData) {
      console.error('Cannot update session: No user data provided');
      return;
    }

    try {
      // Only include fields that should be updated in the session
      const updateData: Record<string, any> = {};
      
      // Update basic user fields if they exist in the userData
      if (userData.name !== undefined) updateData.name = userData.name;
      if (userData.email !== undefined) updateData.email = userData.email;
      
      // Update school-related fields if they exist
      if (userData.schoolId !== undefined) {
        updateData.schoolId = userData.schoolId;
      }
      
      // Call the Next Auth update method to update the session
      await update({
        user: updateData
      });
      
      return true;
    } catch (error) {
      console.error('Failed to update session:', error);
      return false;
    }
  };

  return {
    updateSessionUser,
    session,
    status
  };
}