'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { EUserRole } from '@/config/enums/user';
import { SchoolCustomization } from '@/components/organisms/SchoolCustomization/SchoolCustomization';

export default function SchoolCustomizationPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Redirect if not authenticated or not INDEPENDENT_TEACHER
  React.useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/sign-in');
      return;
    }

    if (session.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
      router.push('/'); // Redirect to home if not INDEPENDENT_TEACHER
      return;
    }
  }, [session, status, router]);

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  // Don't render anything if redirecting
  if (!session || session.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
    return null;
  }

  return <SchoolCustomization />;
}
