@import 'tailwindcss';
@plugin "daisyui" {
  themes: light --default;
};
@plugin "daisyui/theme" {
  name: 'light';
  default: true;
  --color-primary: #3872fa;
  --color-accent: #21272A;
  --color-accent-content:#666666;
  --color-gray-300:#747474;
  --color-error: #ff0000;
  --color-blue: #3872fa;
    --color-secondary: #333333;
    --color-secondary-content: #484848;

  /* font size */
  --text-sm: 0.875rem;
}
@theme{
  --color-grey-28: #484848;
  --text-small: 0.75rem;
  --text-large: 1.125rem;
  --text-xlarge: 1.75rem;
  --color-black-100:#111111;
  --color-gray-200:#DDE1E6;
  --color-dark-gray-300:#F1F1F1;
  --color-dark-gray-400:#747474;
  --color-dark-gray-500:#697077;
  --color-dark-gray: #333333;
  --color-bright-blue: #0070F3
}

/* Custom sticky header with -16px offset */
.sticky-header-offset {
  position: sticky;
  top: -16px;
  z-index: 10;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
