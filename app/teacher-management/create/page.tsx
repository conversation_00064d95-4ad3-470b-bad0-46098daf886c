'use client';

import React from 'react';
import { EUserRole } from '@/config/enums/user';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { UserForm } from '@/components/organisms/UserForm/UserForm';

const CreateTeacherPage = () => {
  // Create the header component
  const header = (
    <ListingHeader
      title="Create Teacher"
      subtitle="Add a new teacher to the system"
      buttonProps={{
        label: 'Back to Teachers',
        href: '/teacher-management',
        variant: 'secondary',
      }}
    />
  );

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <ListingTemplate
        header={header}
        table={
          <UserForm
            defaultRole={EUserRole.TEACHER}
            redirectPath="/teacher-management"
            formTitle="Teacher Information"
            formSubtitle="Basic information about the teacher"
            successMessage="Teacher created successfully!"
            buttonText="Create Teacher"
            userType="teacher"
          />
        }
      />
    </div>
  );
};

export default CreateTeacherPage;
