'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { requiredString } from '@/utils/zod';
import { EUserRole } from '@/config/enums/user';
import { handleGetUserByIdAction, handleUpdateUserAction } from '@/actions/user.action';
import { handleGetAllSchoolsAction, fetchSchools } from '@/actions/school.action';
import {
  ArrowLeft,
  Loader2,
  User,
  Mail,
  Lock,
  ShieldCheck,
  AlertCircle,
  CheckCircle,
  UserCog,
  Building,
} from 'lucide-react';
import Link from 'next/link';
import { SchoolSelector } from '@/components/molecules/FormItems/SchoolSelector';
import { FormField } from '@/components/molecules/FormField/FormField';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { Tooltip } from '@/components/atoms/Tooltip/Tooltip';
import { Input } from '@/components/atoms/Input/Input';
import { cn } from '@/utils/cn';
import { DetailTemplate } from '@/components/templates/DetailTemplate/DetailTemplate';

// Define the form schema using zod
const updateUserSchema = z.object({
  name: requiredString,
  email: requiredString.email('Please enter a valid email address'),
  // Make password optional for updates
  password: z.string().optional(),
  confirmPassword: z.string().optional(),
  role: z.nativeEnum(EUserRole),
  schoolId: z.string().optional(),
});

// Add validation for password confirmation only if password is provided
const updateUserFormSchema = updateUserSchema.refine(
  (data) => !data.password || data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

type UpdateUserFormValues = z.infer<typeof updateUserFormSchema>;

const EditUserPage = () => {
  const params = useParams();
  const userId = params.id as string;

  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  // Initialize the form
  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors, isDirty },
    setValue,
    reset,
  } = useForm<UpdateUserFormValues>({
    resolver: zodResolver(updateUserFormSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: EUserRole.TEACHER,
      schoolId: '',
    },
    mode: 'onChange', // Enable real-time validation
  });

  // Watch the role field to conditionally render fields
  const selectedRole = watch('role');

  // Fetch user data when the component mounts
  useEffect(() => {
    const fetchUser = async () => {
      setIsLoading(true);
      try {
        const response = await handleGetUserByIdAction(userId);
        if (response.status === 'success' && response.data) {
          setUser(response.data);
          // Pre-populate the form with user data
          reset({
            name: response.data.name,
            email: response.data.email,
            password: '',
            confirmPassword: '',
            role: response.data.role,
            schoolId: response.data.schoolId || '',
          });
        } else {
          setFormError('Failed to fetch user details');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        setFormError('An unexpected error occurred while fetching user details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [userId, reset]);

  // Fetch schools when the component mounts or when the role changes to SCHOOL_MANAGER or TEACHER
  useEffect(() => {
    const fetchSchools = async () => {
      if (selectedRole === EUserRole.SCHOOL_MANAGER || selectedRole === EUserRole.TEACHER) {
        try {
          const response = await handleGetAllSchoolsAction();
          if (response.status === 'success' && response.data) {
            setSchools(response.data.map(school => ({ id: school.id, name: school.name })));
          } else {
            if (response.status !== 'success') {
              console.error('Failed to fetch schools:', response.message);
            }
          }
        } catch (error) {
          console.error('Error fetching schools:', error);
        }
      }
    };

    fetchSchools();
  }, [selectedRole]);

  // Handle form submission
  const onSubmit = async (data: UpdateUserFormValues) => {
    setIsSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Remove confirmPassword from the payload
      const { confirmPassword, ...payload } = data;

      // Remove password if it's empty
      if (!payload.password) {
        delete payload.password;
      }

      const response = await handleUpdateUserAction(userId, payload);

      if (response.status === 'success') {
        setFormSuccess('User updated successfully!');
        // Redirect to the user details page after a short delay
        setTimeout(() => {
          router.push(`/users-management`);
        }, 2000);
      } else {
        setFormError(Array.isArray(response.message) ? response.message.join(', ') : response.message);
      }
    } catch (error: any) {
      setFormError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get role badge styling
  const getRoleBadgeClass = (role: EUserRole) => {
    switch (role) {
      case EUserRole.ADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case EUserRole.TEACHER:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case EUserRole.SCHOOL_MANAGER:
        return 'bg-green-100 text-green-800 border-green-200';
      case EUserRole.STUDENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get role description
  const getRoleDescription = (role: EUserRole) => {
    switch (role) {
      case EUserRole.ADMIN:
        return 'Full access to manage all schools, users, and system settings';
      case EUserRole.SCHOOL_MANAGER:
        return 'Manages teachers and resources within their assigned school';
      case EUserRole.TEACHER:
        return 'Creates and manages worksheets and educational content for students';
      case EUserRole.STUDENT:
        return 'Accesses educational content assigned by teachers';
      default:
        return '';
    }
  };

  // Loading state
  const loadingContent = (
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
        <Loader2 size={32} className="animate-spin text-blue-600" />
      </div>
      <p className="text-lg font-medium text-gray-700">Loading user details...</p>
      <p className="text-sm text-gray-500 mt-2">Please wait while we fetch the user information</p>
    </div>
  );

  // Header content
  const headerContent = (
    <>
      {/* Back button */}
      <div className="mb-6">
        <Link 
          href="/users-management" 
          className="inline-flex items-center gap-2 text-gray-900 hover:text-black transition-all duration-300 font-medium group"
        >
          <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Users
        </Link>
      </div>

      {/* User Profile Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
          <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
            <UserCog size={32} />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-800">{user?.name || 'User'}</h1>
            <div className="flex flex-wrap items-center gap-2 mt-2">
              <span className="text-gray-500 flex items-center gap-1">
                <Mail size={16} /> {user?.email}
              </span>
              {user?.role && (
                <span className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getRoleBadgeClass(user.role)}`}>
                  {user.role}
                </span>
              )}
              {user?.schoolId && (
                <span className="text-gray-500 flex items-center gap-1">
                  <Building size={16} /> {schools.find(s => s.id === user.schoolId)?.name || 'School'}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );

  // Error content
  const errorContent = formError ? (
    <div className="bg-blue-50 border border-blue-200 p-4 mb-6 rounded-lg shadow-sm">
      <div className="flex items-center">
        <div className="flex-shrink-0 text-blue-500">
          <AlertCircle size={24} />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-blue-800">Error</h3>
          <p className="text-sm text-blue-700 mt-1">{formError}</p>
        </div>
      </div>
    </div>
  ) : null;

  // Success content
  const successContent = formSuccess ? (
    <div className="bg-green-50 border border-green-200 p-4 mb-6 rounded-lg shadow-sm">
      <div className="flex items-center">
        <div className="flex-shrink-0 text-green-500">
          <CheckCircle size={24} />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-green-800">Success</h3>
          <p className="text-sm text-green-700 mt-1">{formSuccess}</p>
        </div>
      </div>
    </div>
  ) : null;

  // Main content
  const mainContent = (
    <form onSubmit={handleSubmit(onSubmit)} className="overflow-hidden rounded-[0.5rem] border border-gray-200 shadow-sm bg-white">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8">
        {/* User Information */}
        <div className="space-y-6">
          <div className="pb-3 mb-4 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                <User size={20} />
              </div>
              User Information
            </h2>
            <p className="text-sm text-gray-600 mt-1.5">Basic information about the user</p>
          </div>

          <FormField
            label="Full Name"
            error={errors.name?.message}
            icon={<User size={18} />}
            required
          >
            <Input
              {...register('name')}
              placeholder="Enter full name"
              className={cn(
                'pl-10', // Add left padding for icon
                errors.name ? 'border-blue-300 focus:ring-blue-500 focus:border-blue-500' : ''
              )}
            />
          </FormField>

          <FormField
            label="Email Address"
            error={errors.email?.message}
            icon={<Mail size={18} />}
            required
          >
            <Input
              {...register('email')}
              type="email"
              placeholder="Enter email address"
              className={cn(
                'pl-10', // Add left padding for icon
                errors.email ? 'border-blue-300 focus:ring-blue-500 focus:border-blue-500' : ''
              )}
            />
          </FormField>

          <FormField
            label="Role"
            error={errors.role?.message}
            required
          >
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {Object.values(EUserRole).map((role) => (
                  <div 
                    key={role}
                    className={cn(
                      'relative rounded-lg border p-4 cursor-pointer transition-all duration-300',
                      selectedRole === role
                        ? 'border-blue-500 bg-blue-50 shadow-sm'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
                    )}
                    onClick={() => setValue('role', role, { shouldValidate: true, shouldDirty: true })}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center ${selectedRole === role ? 'bg-blue-500' : 'border border-gray-300'}`}>
                          {selectedRole === role && (
                            <div className="w-2.5 h-2.5 rounded-full bg-white" />
                          )}
                        </div>
                        <span className={`ml-2 font-medium ${selectedRole === role ? 'text-blue-700' : 'text-gray-700'}`}>
                          {role}
                        </span>
                      </div>
                      <div className={`p-1.5 rounded-full ${getRoleBadgeClass(role)}`}>
                        <ShieldCheck size={16} />
                      </div>
                    </div>
                    <p className="mt-2 text-xs text-gray-500">{getRoleDescription(role)}</p>
                  </div>
                ))}
              </div>
            </div>
          </FormField>
        </div>

        {/* Password and School Information */}
        <div className="space-y-6">
          <div className="pb-3 mb-4 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                <Lock size={20} />
              </div>
              Security & Assignment
            </h2>
            <p className="text-sm text-gray-600 mt-1.5">Password and school assignment</p>
          </div>

          <FormField
            label="Password"
            error={errors.password?.message}
            hint="Leave blank to keep current password"
          >
            <PasswordInput
              {...register('password')}
              className={errors.password ? 'border-blue-300 focus:ring-blue-500 focus:border-blue-500' : ''}
              placeholder="••••••••"
              showStrengthMeter={true}
            />
          </FormField>

          <FormField
            label="Confirm Password"
            error={errors.confirmPassword?.message}
            hint="Re-enter the password to confirm"
          >
            <PasswordInput
              {...register('confirmPassword')}
              className={errors.confirmPassword ? 'border-blue-300 focus:ring-blue-500 focus:border-blue-500' : ''}
              placeholder="••••••••"
            />
          </FormField>

          {/* School Selection - Only show for School Manager and Teacher roles */}
          {(selectedRole === EUserRole.SCHOOL_MANAGER || selectedRole === EUserRole.TEACHER) && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <label className="block text-sm font-medium text-gray-700">
                  Select School
                  <span className="text-red-500 ml-1">*</span>
                </label>
                <Tooltip 
                  content="The school this user will be assigned to"
                  position="top"
                />
              </div>
              <SchoolSelector
                control={control}
                name="schoolId"
                required={true}
                error={errors.schoolId}
                fetchSchools={fetchSchools}
                showCreateButton={false}
              />
            </div>
          )}
        </div>
      </div>

      {/* Form Actions */}
      <div className="px-5 py-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
        <Link 
          href="/users-management" 
          className="px-5 py-2.5 bg-white text-gray-700 rounded-lg border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
        >
          Cancel
        </Link>
        <button
          type="submit"
          className={`px-5 py-2.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md font-medium flex items-center justify-center gap-2 min-w-[140px] ${
            isSubmitting || !isDirty 
              ? 'bg-gray-400 text-white cursor-not-allowed' 
              : 'bg-gray-700 text-white hover:bg-gray-800 focus:ring-gray-500'
          }`}
          disabled={isSubmitting || !isDirty}
        >
          {isSubmitting ? (
            <>
              <Loader2 size={18} className="animate-spin" />
              Updating...
            </>
          ) : (
            <>
              <CheckCircle size={18} />
              Update User
            </>
          )}
        </button>
      </div>
    </form>
  );

  // Define breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Users Management', href: '/users-management' },
    { label: 'Edit User', href: '#' },
  ];

  // Use DetailTemplate for consistent layout
  return (
    <DetailTemplate
      header={headerContent}
      content={isLoading ? null : mainContent}
      error={errorContent || (successContent && null)}
      loading={isLoading ? loadingContent : null}
      breadcrumbItems={breadcrumbItems}
    />
  );
};

export default EditUserPage;
