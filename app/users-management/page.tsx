'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { handleGetAllUsersAction } from '@/actions/user.action';
import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { UsersTable } from '@/components/organisms/UsersTable/UsersTable';
import { Breadcrumb } from '@/components/atoms';
import { IUserResponse } from '@/apis/userApi';

const UsersManagementPage = () => {
  const { data: session } = useSession();
  const [users, setUsers] = useState<IUserResponse[]>([]);
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch all users and schools
  useEffect(() => {
    const fetchData = async () => {
      if (!session) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        // Fetch all users
        const usersResponse = await handleGetAllUsersAction();
        
        // Fetch schools
        const schoolsResponse = await handleGetAllSchoolsAction();

        // Process users data
        if (usersResponse.status === 'success') {
          const usersWithStatus = (usersResponse.data || []).map(user => ({
            ...user,
            status: !(user as any).delete_at ? 'active' : ['inactive', 'pending', 'suspended'][Math.floor(Math.random() * 3)],
            lastActivity: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
          }));

          setUsers(usersWithStatus);
        } else {
          const errorMessage = typeof usersResponse.message === 'string' ? usersResponse.message : 'Failed to fetch users';
          setError(errorMessage);
        }

        // Process schools data
        if (schoolsResponse.status === 'success') {
          const schoolsData = schoolsResponse.data || [];
          setSchools(schoolsData.map(school => ({ id: school.id, name: school.name })));
        }
      } catch (err) {
        console.error('Exception while fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [session]);

  // Create the header component
  const header = (
    <>
      {/* Breadcrumb Navigation */}
      <div className="my-3">
        <Breadcrumb items={[
          { label: 'Home', href: '/' },
          { label: 'Users Management' },
        ]} />
      </div>
      <ListingHeader 
        title="Users Management" 
        subtitle="Manage all users in the system"
        buttonProps={{
          label: "Create User",
          href: "/users-management/create",
          variant: "primary",
        }}
      />
    </>
  );

  return (
    <ListingTemplate 
      header={header}
      table={
        <UsersTable 
          users={users} 
          error={error} 
          isLoading={isLoading}
          schools={schools}
        />
      }
    />
  );
};

export default UsersManagementPage;
