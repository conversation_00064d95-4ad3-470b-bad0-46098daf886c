'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { EUserRole } from '@/config/enums/user';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { UserForm } from '@/components/organisms/UserForm/UserForm';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';
import { Breadcrumb } from '@/components/atoms';
import { Home } from 'lucide-react';

const CreateUserPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showSchoolModal, setShowSchoolModal] = useState(false);
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  
  // Get role and schoolId from URL query parameters
  const roleParam = searchParams.get('role');
  const schoolIdParam = searchParams.get('schoolId');
  const [selectedSchoolId, setSelectedSchoolId] = useState<string | undefined>(schoolIdParam || undefined);
  const defaultRole = roleParam as EUserRole | undefined;

  // Fetch schools when the component mounts
  useEffect(() => {
    const fetchSchools = async () => {
      setIsLoadingSchools(true);
      try {
        const response = await handleGetAllSchoolsAction();
        if (response.status === 'success' && response.data) {
          setSchools(response.data.map(school => ({ id: school.id, name: school.name })));
        } else {
          if (response.status !== 'success') {
            console.error('Failed to fetch schools:', response.message);
          }
        }
      } catch (error) {
        console.error('Error fetching schools:', error);
      } finally {
        setIsLoadingSchools(false);
      }
    };

    fetchSchools();
  }, []);

  // Create the header component
  const header = (
    <>
      {/* Breadcrumb Navigation */}
      <div className="my-3">
        <Breadcrumb items={[
          { label: 'Home', href: '/' },
          { label: defaultRole === EUserRole.TEACHER ? 'Teachers Management' : 'Users Management', href: defaultRole === EUserRole.TEACHER ? '/teacher-management' : '/users-management' },
          { label: defaultRole === EUserRole.TEACHER ? 'Add Teacher' : 'Create User' },
        ]} />
      </div>
      <ListingHeader 
        title={defaultRole === EUserRole.TEACHER ? "Add Teacher" : "Create User"} 
        subtitle={defaultRole === EUserRole.TEACHER ? "Add a new teacher to the system" : "Add a new user to the system"}
        buttonProps={{
          label: defaultRole === EUserRole.TEACHER ? "Back to School" : "Back to Users",
          href: defaultRole === EUserRole.TEACHER && schoolIdParam ? `/school-management/${schoolIdParam}` : "/users-management",
          variant: "secondary",
        }}
      />
    </>
  );

  // Handle school creation success
  const handleSchoolCreationSuccess = (data: { id: string; name: string }) => {
    setSchools(prev => [...prev, { id: data.id, name: data.name }]);
    setSelectedSchoolId(data.id);
  };

  return (
    <>
      <ListingTemplate 
        header={header}
        table={
          <UserForm 
            schools={schools}
            isLoadingSchools={isLoadingSchools}
            onCreateSchool={() => setShowSchoolModal(true)}
            selectedSchoolId={selectedSchoolId}
            defaultRole={defaultRole}
            userType={defaultRole === EUserRole.TEACHER ? 'teacher' : undefined}
            formTitle={defaultRole === EUserRole.TEACHER ? 'Teacher Information' : 'User Information'}
            formSubtitle={defaultRole === EUserRole.TEACHER ? 'Basic information about the teacher' : 'Basic information about the user'}
            buttonText={defaultRole === EUserRole.TEACHER ? 'Create Teacher' : 'Create User'}
          />
        }
      />

      {/* Create School Modal */}
      <SchoolModal 
        isOpen={showSchoolModal}
        onClose={() => setShowSchoolModal(false)}
        onSuccess={handleSchoolCreationSuccess}
        onError={setFormError}
      />
    </>
  );
};

export default CreateUserPage;
