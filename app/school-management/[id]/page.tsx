'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';

import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { DetailTemplate } from '@/components/templates/DetailTemplate/DetailTemplate';
import { Button } from '@/components/atoms/Button/Button';

import { SchoolDetailCard } from '@/components/organisms/SchoolDetailCard/SchoolDetailCard';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';
import { handleGetSchoolByIdAction, handleDeleteSchoolAction } from '@/actions/school.action';
import { ExaminationFormatManager } from '@/components/organisms/ExaminationFormatManager/ExaminationFormatManager';
import { NarrativeStructureManager } from '@/components/organisms/NarrativeStructureManager/NarrativeStructureManager';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

// Import icons
import { BarChart2, Users, FileText, Plus, Home } from 'lucide-react';

// Import types
import type { ISchoolResponse } from '@/apis/schoolApi';
import { formatApiMessage } from '@/utils/formatApiMessage';
import { EUserRole } from '@/config/enums/user';

export default function SchoolDetailPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const params = useParams();
  const schoolId = params?.id as string; // Get school ID from URL

  const [schoolDetails, setSchoolDetails] = useState<ISchoolResponse | null>(null); // Use ISchoolResponse
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false); // State for edit modal
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [activeTab, setActiveTab] = useState<'format' | 'narrative'>('format'); // Tab state for assessment materials

  // State for API messages
  const [apiMessage, setApiMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Fetch school details
  const fetchSchoolDetails = useCallback(async () => {
    setIsLoading(true);
    setApiMessage(null);

    try {
      const response = await handleGetSchoolByIdAction(schoolId);
      if (response.status === 'success' && response.data) {
        setSchoolDetails(response.data);
      } else if (response.status === 'error') {
        setApiMessage({
          type: 'error',
          message: formatApiMessage(response.message) || 'Failed to fetch school details',
        });
      }
    } catch (error: any) {
      setApiMessage({
        type: 'error',
        message: error.message || 'An error occurred while fetching school details',
      });
    } finally {
      setIsLoading(false);
    }
  }, [schoolId]);

  useEffect(() => {
    if (schoolId) {
      fetchSchoolDetails();
    }
  }, [fetchSchoolDetails, schoolId, refreshTrigger]);

  // Auto-dismiss API messages after 5 seconds
  useEffect(() => {
    if (apiMessage) {
      const timer = setTimeout(() => {
        setApiMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [apiMessage]);

  // Handle opening the edit modal
  const handleOpenEditModal = () => {
    setIsEditModalOpen(true);
  };

  // Handle closing the edit modal
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  // Handle refresh trigger
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Handle edit success
  const handleEditSuccess = (data: ISchoolResponse | { id: string; name: string }) => {
    // If it's a full ISchoolResponse, use it directly
    if ('address' in data) {
      setSchoolDetails(data);
    } else {
      // If it's just basic data, merge with existing school details
      setSchoolDetails(prev => prev ? { ...prev, ...data } : null);
    }
    setApiMessage({
      type: 'success',
      message: 'School updated successfully',
    });
    handleCloseEditModal();
    // Trigger refresh to update examination format data
    handleRefresh();
  };

  // Handle edit error
  const handleEditError = (errorMessage: string) => {
    setApiMessage({
      type: 'error',
      message: errorMessage,
    });
  };

  // Handle delete school
  const handleDeleteSchool = async () => {
    try {
      const response = await handleDeleteSchoolAction(schoolId);
      if (response.status === 'success') {
        router.push('/school-management');
      } else {
        setApiMessage({
          type: 'error',
          message: formatApiMessage(response.message) || 'Failed to delete school',
        });
      }
    } catch (error: any) {
      setApiMessage({
        type: 'error',
        message: error.message || 'An error occurred while deleting the school',
      });
    }
  };

  return (
    <DashboardTemplate
      pageContainerType="default"
      sidebarItems={[]} // Sidebar items will be populated based on user role
      userMenuDropdownProps={{
        userName: session?.user?.name || undefined,
        userEmail: session?.user?.email || undefined
      }}
    >
      <DetailTemplate
        breadcrumbItems={[
          {
            label: 'Home',
            href: '/',
            icon: <Home size={14} />
          },
          {
            label: 'Schools',
            href: '/school-management'
          },
          {
            label: schoolDetails?.name || 'School Details'
          }
        ]}
        header={null}
        content={
          <>
            {/* API Message Display */}
            {apiMessage && (
              <AlertMessage
                type={apiMessage.type}
                message={apiMessage.message}
              />
            )}

            {/* Quick Actions Floating Button */}
            <div className="fixed bottom-8 right-8 z-10">
              <div className="dropdown dropdown-top dropdown-end">
                <Button 
                  className="!w-auto h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                  variant="primary"
                  tabIndex={0}
                >
                  <Plus size={24} />
                </Button>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow-lg bg-white rounded-box w-52 mt-4">
                  <li>
                    <a 
                      className="flex items-center gap-2 text-sm py-2.5"
                      onClick={() => router.push(`/users-management/create?role=${EUserRole.TEACHER}&schoolId=${schoolId}`)}
                    >
                      <Users size={16} />
                      <span>Add Teacher</span>
                    </a>
                  </li>
                  <li>
                    <a className="flex items-center gap-2 text-sm py-2.5">
                      <FileText size={16} />
                      <span>Upload Assessment</span>
                    </a>
                  </li>
                  <li>
                    <a className="flex items-center gap-2 text-sm py-2.5">
                      <BarChart2 size={16} />
                      <span>View Analytics</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* School Details Card - Takes 1/3 of the grid */}
              <div className="lg:col-span-1">
                {isLoading ? (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 h-full">
                    <div className="animate-pulse space-y-4">
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 bg-gray-200 rounded-lg"></div>
                        <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                        <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <SchoolDetailCard
                    school={schoolDetails as ISchoolResponse}
                    onEdit={handleOpenEditModal}
                    onDelete={handleDeleteSchool}
                  />
                )}
              </div>

              {/* Overview Section with Data Visualization - Takes 2/3 of the grid */}
              <div className="lg:col-span-2 space-y-6">
                {/* Quick Stats with Visualizations */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-5">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">School Overview</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Teachers Stat */}
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-100 flex items-center">
                      <div className="bg-blue-100 rounded-full p-3 mr-3">
                        <Users className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-600 font-medium">Teachers</p>
                        <div className="flex items-center">
                          <h4 className="text-2xl font-bold text-blue-700">24</h4>
                          <span className="ml-2 text-xs bg-blue-200 text-blue-800 px-1.5 py-0.5 rounded">+3 this month</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Students Stat */}
                    <div className="bg-green-50 rounded-lg p-4 border border-green-100 flex items-center">
                      <div className="bg-green-100 rounded-full p-3 mr-3">
                        <Users className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-green-600 font-medium">Students</p>
                        <div className="flex items-center">
                          <h4 className="text-2xl font-bold text-green-700">342</h4>
                          <span className="ml-2 text-xs bg-green-200 text-green-800 px-1.5 py-0.5 rounded">+12 this month</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Worksheets Stat */}
                    <div className="bg-amber-50 rounded-lg p-4 border border-amber-100 flex items-center">
                      <div className="bg-amber-100 rounded-full p-3 mr-3">
                        <FileText className="h-6 w-6 text-amber-600" />
                      </div>
                      <div>
                        <p className="text-sm text-amber-600 font-medium">Worksheets</p>
                        <div className="flex items-center">
                          <h4 className="text-2xl font-bold text-amber-700">156</h4>
                          <span className="ml-2 text-xs bg-amber-200 text-amber-800 px-1.5 py-0.5 rounded">+8 this week</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Progress Bars */}
                  <div className="mt-6 space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">Assessment Completion</span>
                        <span className="text-sm font-medium text-gray-700">75%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">Teacher Engagement</span>
                        <span className="text-sm font-medium text-gray-700">92%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Assessment Materials Section */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                  <div className="border-b border-gray-200">
                    <div className="flex">
                      <button
                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'format' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}`}
                        onClick={() => setActiveTab('format')}
                      >
                        Examination Format
                      </button>
                      <button
                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'narrative' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}`}
                        onClick={() => setActiveTab('narrative')}
                      >
                        Narrative Structure
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-5">
                    {isLoading ? (
                      <div className="animate-pulse space-y-4 p-4">
                        <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-full"></div>
                          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                          <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                        </div>
                      </div>
                    ) : activeTab === 'format' ? (
                      <ExaminationFormatManager
                        schoolId={schoolId}
                        schoolName={schoolDetails?.name}
                        refreshTrigger={refreshTrigger}
                      />
                    ) : (
                      <NarrativeStructureManager
                        schoolId={schoolId}
                        schoolName={schoolDetails?.name || ''}
                      />
                    )}
                  </div>
                </div>
                
                {/* Recent Activity Feed */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-5">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">Recent Activity</h3>
                  
                  <div className="space-y-4">
                    {/* Activity Items */}
                    <div className="flex items-start">
                      <div className="bg-blue-100 rounded-full p-2 mr-3">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">New examination format uploaded</p>
                        <p className="text-xs text-gray-500">2 hours ago by John Smith</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-3">
                        <Users className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">3 new teachers added to school</p>
                        <p className="text-xs text-gray-500">Yesterday by Admin</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-amber-100 rounded-full p-2 mr-3">
                        <BarChart2 className="h-4 w-4 text-amber-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Monthly performance report generated</p>
                        <p className="text-xs text-gray-500">3 days ago by System</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        }
      />

      {/* School Edit Modal */}
      <SchoolModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        onSuccess={handleEditSuccess}
        onError={handleEditError}
        schoolToEdit={schoolDetails}
      />
    </DashboardTemplate>
  );
}
