'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Loader2, School as SchoolIcon } from 'lucide-react'; // Keep only necessary lucide icons

import DashboardTemplate from '@/components/templates/Dashboard/Dashboard'; // Keep if used by ListingTemplate or for loading/auth states
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { SchoolsTable } from '@/components/organisms/SchoolsTable/SchoolsTable'; // Import the new table component
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal'; // Import SchoolModal
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay'; // For displaying errors

import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { TTransformResponse } from '@/apis/transformResponse';
import { ISchoolResponse } from '@/apis/schoolApi';
import { UserMenuDropdownProps } from '@/components/molecules/UserMenuDropdown/UserMenuDropdown';
import { SidebarItem } from '@/components/organisms/Sidebar/Sidebar';

// Helper to handle message formatting (string or array of validation errors)
const formatApiMessage = (message: string | any[] | undefined): string => {
  if (Array.isArray(message)) {
    return message.map(m => typeof m === 'object' && m.message ? m.message : String(m)).join(', ');
  }
  return String(message || '');
};

export default function SchoolsListingPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();

  const [allSchools, setAllSchools] = useState<ISchoolResponse[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [apiMessage, setApiMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  
  const [isSchoolModalOpen, setIsSchoolModalOpen] = useState<boolean>(false);
  const [schoolToEdit, setSchoolToEdit] = useState<ISchoolResponse | null>(null);

  const loadSchools = useCallback(async () => {
    setIsLoading(true);
    setApiMessage(null);
    try {
      const response: TTransformResponse<ISchoolResponse[]> = await handleGetAllSchoolsAction();
      if (response.status === 'success' && response.data) {
        setAllSchools(response.data);
      } else if (response.status === 'error') {
        setAllSchools([]);
        const message = formatApiMessage(response.message) || 'Failed to fetch schools';
        setApiMessage({ type: 'error', message });
      } else {
        setAllSchools([]);
        setApiMessage({ type: 'error', message: 'Received an unexpected response from the server.' });
      }
    } catch (error: any) {
      setApiMessage({
        type: 'error',
        message: formatApiMessage(error.message) || 'An unexpected error occurred while fetching schools.',
      });
      setAllSchools([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      loadSchools();
    }
  }, [sessionStatus, loadSchools]);

  // Default props for DashboardTemplate if not passed from a higher level
  // These might be needed if ListingTemplate doesn't handle auth/loading states directly
  const defaultSidebarItems: SidebarItem[] = [
    { label: "Dashboard", href: "/admin/dashboard", icon: <SchoolIcon size={18} /> },
    { label: "Schools", href: "/school-management", icon: <SchoolIcon size={18} /> }
  ];
  const defaultUserMenuDropdownProps: UserMenuDropdownProps = {
    userName: session?.user?.name || undefined,
    userEmail: session?.user?.email || undefined,
  };

  if (sessionStatus === 'loading') {
    return (
      <DashboardTemplate sidebarItems={defaultSidebarItems} userMenuDropdownProps={defaultUserMenuDropdownProps} schoolInfo={null}>
        <>
          <Loader2 size={48} className="animate-spin text-blue-600 mb-4" />
          <p className="text-lg font-medium text-gray-700">Loading session...</p>
        </>
      </DashboardTemplate>
    );
  }

  if (sessionStatus === 'unauthenticated') {
    router.push("/auth/sign-in");
    return (
      <DashboardTemplate sidebarItems={defaultSidebarItems} userMenuDropdownProps={defaultUserMenuDropdownProps} schoolInfo={null}>
        <p className="text-center">Redirecting to sign-in...</p>
      </DashboardTemplate>
    );
  }

  // Modal Handlers
  const handleOpenCreateModal = () => {
    setSchoolToEdit(null); // Ensure it's in create mode
    setIsSchoolModalOpen(true);
  };

  const handleOpenEditModal = (school: ISchoolResponse) => {
    setSchoolToEdit(school);
    setIsSchoolModalOpen(true);
  };

  const handleCloseSchoolModal = () => {
    setIsSchoolModalOpen(false);
    setSchoolToEdit(null);
  };

  const handleSchoolModalSuccess = (data: ISchoolResponse | { id: string; name: string }) => {
    loadSchools(); // Refresh the list
    setApiMessage({ 
      type: 'success', 
      message: schoolToEdit ? 'School updated successfully!' : 'School created successfully!' 
    });
    handleCloseSchoolModal();
  };

  const handleSchoolModalError = (message: string) => {
    setApiMessage({ 
      type: 'error', 
      message: `Operation failed: ${formatApiMessage(message)}` 
    });
    // Modal remains open for user to correct
  };


  const headerComponent = (
    <ListingHeader
      title="Schools Management"
      subtitle="Manage all schools in the system"
      buttonProps={{
        label: "Create School",
        onClick: handleOpenCreateModal, // Changed from href to onClick
        variant: "primary",
      }}
    />
  );

  const tableComponent = (
    <SchoolsTable
      schools={allSchools}
      isLoading={isLoading}
      apiMessage={apiMessage}
      setApiMessage={setApiMessage}
      onRefresh={loadSchools}
      onEditSchool={handleOpenEditModal} // Pass the handler
      // Props like tableTitle, entityName, etc., are defaulted in SchoolsTable
    />
  );

  const errorDisplayComponent = apiMessage && apiMessage.type === 'error' && !isLoading && allSchools.length === 0 ? (
    <ErrorDisplay error={apiMessage.message} />
  ) : null;


  return (
    <>
      {/* Display general page error here if not handled by SchoolsTable or if it's a fetch error before table renders */}
      {errorDisplayComponent}
      <ListingTemplate
        header={headerComponent}
        table={tableComponent}
        // ListingTemplate might not need sidebarItems and userMenuDropdownProps if it's wrapped by DashboardTemplate at a higher level
        // or if it's meant to be more generic.
        // For now, assuming ListingTemplate is self-contained or DashboardTemplate is handled by layout.tsx
      />
      {isSchoolModalOpen && (
        <SchoolModal
          isOpen={isSchoolModalOpen}
          onClose={handleCloseSchoolModal}
          onSuccess={handleSchoolModalSuccess}
          onError={handleSchoolModalError}
          schoolToEdit={schoolToEdit}
        />
      )}
    </>
  );
}
