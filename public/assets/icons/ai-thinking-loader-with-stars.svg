<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <style>
    .loader-container {
      filter: drop-shadow(0 0 8px rgba(56, 114, 250, 0.6));
    }
    .pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    .particle {
      animation-duration: 3s;
      animation-iteration-count: infinite;
      animation-timing-function: ease-in-out;
      transform-origin: center;
    }
    .p1 {
      animation-name: moveParticle1;
      animation-delay: 0s;
    }
    .p2 {
      animation-name: moveParticle2;
      animation-delay: 0.5s;
    }
    .p3 {
      animation-name: moveParticle3;
      animation-delay: 1s;
    }
    .p4 {
      animation-name: moveParticle4;
      animation-delay: 1.5s;
    }
    .star1 {
      animation: twinkle1 3s infinite;
    }
    .star2 {
      animation: twinkle2 4s infinite;
    }
    .thinking-dots {
      animation: thinkingAnimation 1.5s infinite;
    }
    @keyframes pulse {
      0%, 100% {
        opacity: 1;
        r: 30;
      }
      50% {
        opacity: 0.8;
        r: 35;
      }
    }
    @keyframes moveParticle1 {
      0%, 100% { transform: translate(0, 0); opacity: 0; }
      25% { transform: translate(30px, -30px); opacity: 1; }
      75% { transform: translate(60px, 0); opacity: 0; }
    }
    @keyframes moveParticle2 {
      0%, 100% { transform: translate(0, 0); opacity: 0; }
      25% { transform: translate(-30px, -30px); opacity: 1; }
      75% { transform: translate(-60px, 0); opacity: 0; }
    }
    @keyframes moveParticle3 {
      0%, 100% { transform: translate(0, 0); opacity: 0; }
      25% { transform: translate(-30px, 30px); opacity: 1; }
      75% { transform: translate(-60px, 60px); opacity: 0; }
    }
    @keyframes moveParticle4 {
      0%, 100% { transform: translate(0, 0); opacity: 0; }
      25% { transform: translate(30px, 30px); opacity: 1; }
      75% { transform: translate(60px, 60px); opacity: 0; }
    }
    @keyframes thinkingAnimation {
      0% { opacity: 0.3; }
      50% { opacity: 1; }
      100% { opacity: 0.3; }
    }
    @keyframes twinkle1 {
      0%, 100% { opacity: 0.7; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.1); }
    }
    @keyframes twinkle2 {
      0%, 100% { opacity: 0.5; transform: scale(0.9); }
      50% { opacity: 1; transform: scale(1); }
    }
  </style>
  <defs>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#3872FA" stop-opacity="1" />
      <stop offset="100%" stop-color="#1A1C37" stop-opacity="0" />
    </radialGradient>
    <linearGradient id="starGradient1" x1="50%" y1="0%" x2="50%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#CDFF3D"/>
      <stop offset="0.22" stop-color="white"/>
      <stop offset="1" stop-color="#FF9900"/>
    </linearGradient>
    <linearGradient id="starGradient2" x1="50%" y1="0%" x2="50%" y2="100%" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#CDFF3D"/>
      <stop offset="0.22" stop-color="white"/>
      <stop offset="1" stop-color="#FF9900"/>
    </linearGradient>
    <linearGradient id="blueGradient" x1="30%" y1="20%" x2="70%" y2="80%" gradientUnits="userSpaceOnUse">
      <stop offset="0.23" stop-color="#3872FA" stop-opacity="0.75"/>
      <stop offset="1" stop-color="#00FFFF" stop-opacity="0"/>
    </linearGradient>
  </defs>
  
  <g class="loader-container">
    <!-- Background glow -->
    <circle cx="100" cy="100" r="50" fill="url(#glow)" />
    
    <!-- Blue circular gradient from your reference -->
    <circle cx="100" cy="100" r="40" fill="url(#blueGradient)" class="pulse" opacity="0.5" />
    
    <!-- Main star (larger) -->
    <path d="M100.03 99.841C100.285 98.834 101.715 98.834 101.97 99.841L102.425 101.647C103.767 106.963 108.172 111.022 113.566 112.007C114.659 112.207 114.659 113.793 113.566 113.993C108.172 114.978 103.767 119.037 102.425 124.353L101.97 126.159C101.715 127.166 100.285 127.166 100.03 126.159L99.575 124.353C98.233 119.037 93.828 114.978 88.434 113.993C87.341 113.793 87.341 112.207 88.434 112.007C93.828 111.022 98.233 106.963 99.575 101.647L100.03 99.841Z" fill="url(#starGradient1)" class="star1" />
    
    <!-- Second star (smaller) -->
    <path d="M91.305 67.98C91.401 67.155 92.599 67.155 92.695 67.98L92.813 68.99C93.172 72.08 95.528 74.562 98.595 75.083L99.933 75.31C100.71 75.442 100.71 76.558 99.933 76.69L98.595 76.917C95.528 77.438 93.172 79.92 92.813 83.01L92.695 84.02C92.599 84.845 91.401 84.845 91.305 84.02L91.187 83.01C90.828 79.92 88.472 77.438 85.405 76.917L84.067 76.69C83.29 76.558 83.29 75.442 84.067 75.31L85.405 75.083C88.472 74.562 90.828 72.08 91.187 68.99L91.305 67.98Z" fill="url(#starGradient2)" class="star2" />
    
    <!-- Thinking dots -->
    <circle cx="100" cy="135" r="2" fill="#FFFFFF" class="thinking-dots" />
    <circle cx="110" cy="135" r="2" fill="#FFFFFF" class="thinking-dots" />
    <circle cx="120" cy="135" r="2" fill="#FFFFFF" class="thinking-dots" />
    
    <!-- Particles -->
    <circle cx="100" cy="100" r="2" fill="#FFFFFF" class="particle p1" />
    <circle cx="100" cy="100" r="2" fill="#FFFFFF" class="particle p2" />
    <circle cx="100" cy="100" r="2" fill="#FFFFFF" class="particle p3" />
    <circle cx="100" cy="100" r="2" fill="#FFFFFF" class="particle p4" />
  </g>
</svg>