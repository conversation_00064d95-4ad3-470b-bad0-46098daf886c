import { EUserRole } from '@/config/enums/user';
import { TTransformResponse } from './transformResponse';

export interface ISignUpPayload {
  name: string;
  email: string;
  password: string;
  role?: EUserRole;
  schoolId?: string;
}

export interface ISignInPayload {
  email: string;
  password: string;
}

export interface IUserResponse {
  id: string;
  name: string;
  email: string;
  role: EUserRole;
  schoolId?: string | null;
}

export interface ISignInResponse {
  accessToken: string;
  user: IUserResponse;
}

const AUTH_API_BASE = '/auth';

/**
 * Sign up a new user (public endpoint)
 * Corresponds to: POST /auth/sign-up
 * @param payload - The sign-up data
 * @returns The created user profile
 */
export async function signUp(payload: ISignUpPayload): Promise<TTransformResponse<IUserResponse>> {
  try {
    const baseUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || '';
    const endpoint = `${baseUrl}${AUTH_API_BASE}/sign-up`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (response.ok) {
      return {
        status: 'success',
        data: result,
      };
    } else {
      return {
        status: 'error',
        message: result.message || 'Failed to create account',
        statusCode: response.status,
      };
    }
  } catch (error: any) {
    console.error('Error signing up:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while creating account.',
    };
  }
}

/**
 * Sign in a user (public endpoint)
 * Corresponds to: POST /auth/sign-in
 * @param payload - The sign-in credentials
 * @returns The access token and user data
 */
export async function signIn(payload: ISignInPayload): Promise<TTransformResponse<ISignInResponse>> {
  try {
    const baseUrl = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || '';
    const endpoint = `${baseUrl}${AUTH_API_BASE}/sign-in`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (response.ok) {
      return {
        status: 'success',
        data: result,
      };
    } else {
      return {
        status: 'error',
        message: result.message || 'Invalid credentials',
        statusCode: response.status,
      };
    }
  } catch (error: any) {
    console.error('Error signing in:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while signing in.',
    };
  }
}
