import { request } from './request';
import { TTransformResponse } from './transformResponse';

export interface ICreateBrandPayload {
  logo?: string;
  color?: string;
  image?: string;
}

export interface IBrandResponse {
  id: string;
  logo?: string;
  color?: string;
  image?: string;
  createdAt?: string;
  updatedAt?: string;
}

const BRANDS_API_ENDPOINT = '/brands'; // As per documentation

/**
 * Creates a new brand.
 * Corresponds to: POST /brands
 * @param payload - The brand data.
 * @returns The created brand details.
 */
export async function createBrand(payload: ICreateBrandPayload): Promise<TTransformResponse<IBrandResponse>> {
  try {
    const response = await request<IBrandResponse>({
      url: BRANDS_API_ENDPOINT,
      options: {
        method: 'POST',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error creating brand:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while creating the brand.' };
  }
}

/**
 * Fetches all brands.
 * Corresponds to: GET /brands
 * @returns A list of all brands.
 */
export async function getAllBrands(): Promise<TTransformResponse<IBrandResponse[]>> {
  try {
    const response = await request<IBrandResponse[]>({
      url: BRANDS_API_ENDPOINT,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching brands:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching brands.' };
  }
}

/**
 * Fetches a brand by ID.
 * Corresponds to: GET /brands/{id}
 * @param brandId - The ID of the brand to fetch.
 * @returns The brand details.
 */
export async function getBrandById(brandId: string): Promise<TTransformResponse<IBrandResponse>> {
  try {
    const response = await request<IBrandResponse>({
      url: `${BRANDS_API_ENDPOINT}/${brandId}`,
      options: {
        method: 'GET',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching brand:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while fetching the brand.' };
  }
}

export interface IUpdateBrandPayload {
  logo?: string;
  color?: string;
  image?: string;
}

/**
 * Updates an existing brand.
 * Corresponds to: PATCH /brands/{id}
 * @param brandId - The ID of the brand to update.
 * @param payload - The brand data to update.
 * @returns The updated brand details.
 */
export async function updateBrand(brandId: string, payload: IUpdateBrandPayload): Promise<TTransformResponse<IBrandResponse>> {
  try {
    const response = await request<IBrandResponse>({
      url: `${BRANDS_API_ENDPOINT}/${brandId}`,
      options: {
        method: 'PATCH',
        body: JSON.stringify(payload),
      },
    });
    return response;
  } catch (error: any) {
    console.error('Error updating brand:', error);
    return { status: 'error', message: error.message || 'An unexpected error occurred while updating the brand.' };
  }
}
