import { transformResponse } from './transformResponse';
import { TTransformResponse } from './transformResponse';
import { logRBACViolation } from '@/utils/rbacErrorHandler';

/**
 * Client-side request helper function for making API requests with authentication
 * @param url - The URL to make the request to
 * @param options - The request options
 * @param accessToken - The access token for authentication
 * @param noContentType - Whether to omit the Content-Type header (useful for FormData)
 * @returns The transformed response
 */
export const clientRequest = async <T>({
  url,
  options,
  accessToken,
  noContentType = false,
}: {
  url: string;
  options?: RequestInit;
  accessToken?: string;
  noContentType?: boolean;
}): Promise<TTransformResponse<T>> => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.BASE_URL || '';
  const fullUrl = baseUrl + url;

  const headers: HeadersInit = {
    ...(noContentType ? {} : { 'Content-Type': 'application/json' }),
    ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}),
    ...options?.headers as HeadersInit,
  };

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers,
    });

    const result = await transformResponse<T>(response);

    // Handle 403 Forbidden responses with enhanced logging
    if (result.status === 'error' && result.statusCode === 403) {
      // Log RBAC violation for security monitoring
      logRBACViolation({
        attemptedAction: `${options?.method || 'GET'} ${url}`,
        resource: url,
        ip: 'client-side', // Client-side requests don't have direct access to IP
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      });
    }

    return result;
  } catch (error: any) {
    console.error('Request error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while making the request.'
    };
  }
};