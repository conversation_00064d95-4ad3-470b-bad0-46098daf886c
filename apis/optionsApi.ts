import {EAPIEndpoint} from "@/@types/enums/api"
import {request} from "./request"


type TOption={
    "id": string,
    "label": string,
    "value": string,
    "isActive"?: boolean,
    // "optionTypeId": "a2e65144-2db8-472a-9fde-a75490d0871d"
  }

type TUserQuestion= {
    "id": string,
    "key": string,
    "label": string,
    "description": string, // NOTE: Question
    "values": TOption[]
  }

export const getOptionsTypes=()=>{
    const url = `${EAPIEndpoint.OPTIONS}/types`
    return request<TUserQuestion[]>({url})
}