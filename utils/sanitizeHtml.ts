/**
 * Sanitizes HTML content and adds Tailwind CSS classes to HTML elements
 * This helps ensure consistent styling when rendering HTML content
 * 
 * @param html - The HTML string to sanitize and enhance with Tailwind classes
 * @returns The sanitized HTML string with added Tailwind classes
 */
export const sanitizeHtml = (html: string): string => {
  // This is a simple implementation - in a production app, you'd want to use a proper HTML sanitizer
  return html
    // Add Tailwind classes to headings
    .replace(/<h1/g, '<h1 class="text-xl font-bold mb-3 mt-4"')
    .replace(/<h2/g, '<h2 class="text-lg font-bold mb-2 mt-3"')
    .replace(/<h3/g, '<h3 class="text-base font-semibold mb-2 mt-3"')
    .replace(/<h4/g, '<h4 class="text-sm font-semibold mb-2 mt-2"')
    .replace(/<h5/g, '<h5 class="text-sm font-medium mb-1 mt-2"')
    .replace(/<h6/g, '<h6 class="text-xs font-medium mb-1 mt-2"')

    // Add Tailwind classes to paragraphs
    .replace(/<p/g, '<p class="mb-3 text-sm"')

    // Add Tailwind classes to lists
    .replace(/<ul/g, '<ul class="list-disc pl-5 mb-3 mt-2 text-sm"')
    .replace(/<ol/g, '<ol class="list-decimal pl-5 mb-3 mt-2 text-sm"')
    .replace(/<li/g, '<li class="mb-1"')

    // Add Tailwind classes to code and pre
    .replace(/<code/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono"')
    .replace(/<pre/g, '<pre class="bg-gray-100 p-3 rounded mb-3 mt-2 overflow-x-auto text-xs font-mono"')

    // Add Tailwind classes to blockquotes
    .replace(/<blockquote/g, '<blockquote class="border-l-4 border-gray-200 pl-4 italic text-gray-600 mb-3"')

    // Add Tailwind classes to tables
    .replace(/<table/g, '<table class="w-full border-collapse mb-3 text-sm"')
    .replace(/<th/g, '<th class="border border-gray-300 bg-gray-50 p-2 text-left font-medium"')
    .replace(/<td/g, '<td class="border border-gray-300 p-2"')

    // Add Tailwind classes to links
    .replace(/<a /g, '<a class="text-primary underline" ')

    // Add Tailwind classes to images
    .replace(/<img /g, '<img class="max-w-full h-auto rounded my-3" ');
};
