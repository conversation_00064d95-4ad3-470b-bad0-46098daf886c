import { EAPIEndpoint } from '@/@types/enums/api';

/**
 * Utility functions for file URL handling
 */

/**
 * Creates a render URL for a file using the new endpoint format
 * @param fileId The file ID to create a render URL for
 * @returns The render URL in format /files/render/{fileId}
 */
export function createFileRenderUrl(fileId: string): string {
  // Use NEXT_PUBLIC_API_URL for client-side compatibility (PDF generation)
  // Fall back to API_URL for server-side usage
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.API_URL;
  return `${apiUrl}${EAPIEndpoint.FILES_RENDER}/${fileId}`;
}

/**
 * Extracts file ID from various URL formats
 * @param url The URL to extract file ID from
 * @returns The file ID or null if not found
 */
export function extractFileIdFromUrl(url: string): string | null {
  if (!url) {
    console.warn('extractFileIdFromUrl: No URL provided');
    return null;
  }

  console.log('extractFileIdFromUrl: Processing URL:', url);

  // Handle new render endpoint format: /files/render/{fileId}
  const renderMatch = url.match(/\/files\/render\/([^\/\?]+)/);
  if (renderMatch) {
    console.log('extractFileIdFromUrl: Found render format file ID:', renderMatch[1]);
    return renderMatch[1];
  }

  // Handle legacy URL formats if needed
  const legacyMatch = url.match(/\/files\/([^\/\?]+)/);
  if (legacyMatch) {
    console.log('extractFileIdFromUrl: Found legacy format file ID:', legacyMatch[1]);
    return legacyMatch[1];
  }

  console.warn('extractFileIdFromUrl: No file ID found in URL:', url);
  return null;
}

/**
 * Converts any existing file URL to the new render format
 * @param url The existing URL
 * @returns The URL in the new render format, or the original URL if no file ID found
 */
export function convertToRenderUrl(url: string): string {
  // Validate input URL
  if (!url || typeof url !== 'string') {
    console.warn('convertToRenderUrl: Invalid URL provided:', url);
    return '';
  }

  // Check if the URL is already a valid render URL format
  if (url.includes('/files/render/')) {
    return url;
  }

  // Try to extract file ID and create render URL
  try {
    const fileId = extractFileIdFromUrl(url);
    if (fileId) {
      return createFileRenderUrl(fileId);
    }

    // If no file ID found, check if it's a valid URL format
    // If it looks like a valid URL, return it as-is, otherwise return empty string
    if (url.startsWith('http') || url.startsWith('/')) {
      return url;
    }

    console.warn('convertToRenderUrl: Could not process URL:', url);
    return '';
  } catch (error) {
    console.error('convertToRenderUrl: Error processing URL:', url, error);
    return '';
  }
}

export function getFileRenderUrl(fileId: string): string {
  if (!fileId) {
    console.warn('getFileRenderUrl: No file ID provided');
    return '';
  }

  const renderUrl = createFileRenderUrl(fileId);
  return renderUrl;
}
