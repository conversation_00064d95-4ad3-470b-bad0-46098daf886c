import { ISchoolResponse } from '@/apis/schoolApi';

/**
 * Enum for school customization levels
 */
export enum SchoolCustomizationLevel {
  BASIC = 'basic',
  PARTIALLY_CUSTOMIZED = 'partially_customized',
  FULLY_CUSTOMIZED = 'fully_customized'
}

/**
 * Interface for customization assessment result
 */
export interface SchoolCustomizationAssessment {
  level: SchoolCustomizationLevel;
  score: number; // 0-100, higher means more customized
  missingCustomizations: string[];
  completedCustomizations: string[];
  recommendations: string[];
}

/**
 * Determines if a school name follows the auto-generated pattern
 * Auto-generated pattern: "[User Name]'s School"
 */
function isAutoGeneratedSchoolName(schoolName: string, userName?: string): boolean {
  if (!userName) return false;
  
  // Check if school name follows the pattern "[Name]'s School"
  const autoPattern = `${userName}'s School`;
  return schoolName === autoPattern;
}

/**
 * Assesses the customization level of a school
 * @param school - The school data to assess
 * @param userName - The user's name for comparison with auto-generated patterns
 * @param userEmail - The user's email for comparison with school contact email
 * @returns SchoolCustomizationAssessment with detailed analysis
 */
export function assessSchoolCustomization(
  school: ISchoolResponse,
  userName?: string,
  userEmail?: string
): SchoolCustomizationAssessment {
  const missingCustomizations: string[] = [];
  const completedCustomizations: string[] = [];
  const recommendations: string[] = [];
  let score = 0;

  // Check school name customization (20 points)
  if (userName && isAutoGeneratedSchoolName(school.name, userName)) {
    missingCustomizations.push('Custom school name');
    recommendations.push('Choose a unique name that reflects your school\'s identity');
  } else {
    completedCustomizations.push('Custom school name');
    score += 20;
  }

  // Check address customization (15 points)
  if (!school.address || school.address === 'Not specified' || school.address.trim() === '') {
    missingCustomizations.push('School address');
    recommendations.push('Add your school\'s physical address');
  } else {
    completedCustomizations.push('School address');
    score += 15;
  }

  // Check phone number (10 points)
  if (!school.phoneNumber || school.phoneNumber.trim() === '') {
    missingCustomizations.push('Phone number');
    recommendations.push('Add a contact phone number');
  } else {
    completedCustomizations.push('Phone number');
    score += 10;
  }

  // Check registered number (5 points)
  if (!school.registeredNumber || school.registeredNumber.trim() === '') {
    missingCustomizations.push('Registration number');
    recommendations.push('Add your school\'s registration number if applicable');
  } else {
    completedCustomizations.push('Registration number');
    score += 5;
  }

  // Check email customization (10 points)
  if (!school.email || school.email === userEmail) {
    missingCustomizations.push('School contact email');
    recommendations.push('Set a dedicated contact email for your school');
  } else {
    completedCustomizations.push('School contact email');
    score += 10;
  }

  // Check brand customization (40 points total)
  if (!school.brand) {
    missingCustomizations.push('School branding');
    recommendations.push('Create your school\'s visual identity with logo and colors');
  } else {
    let brandScore = 0;
    
    // Logo (20 points)
    if (school.brand.logo) {
      completedCustomizations.push('School logo');
      brandScore += 20;
    } else {
      missingCustomizations.push('School logo');
      recommendations.push('Upload your school\'s logo');
    }

    // Colors (15 points)
    if (school.brand.color) {
      completedCustomizations.push('Brand colors');
      brandScore += 15;
    } else {
      missingCustomizations.push('Brand colors');
      recommendations.push('Choose your school\'s brand colors');
    }

    // Brand image (5 points)
    if (school.brand.image) {
      completedCustomizations.push('Brand image');
      brandScore += 5;
    } else {
      missingCustomizations.push('Brand image');
      recommendations.push('Add a banner or background image');
    }

    score += brandScore;
  }

  // Determine customization level based on score
  let level: SchoolCustomizationLevel;
  if (score >= 80) {
    level = SchoolCustomizationLevel.FULLY_CUSTOMIZED;
  } else if (score >= 40) {
    level = SchoolCustomizationLevel.PARTIALLY_CUSTOMIZED;
  } else {
    level = SchoolCustomizationLevel.BASIC;
  }

  return {
    level,
    score,
    missingCustomizations,
    completedCustomizations,
    recommendations
  };
}

/**
 * Checks if a school is considered "basic" (auto-generated with minimal customization)
 */
export function isBasicSchool(
  school: ISchoolResponse,
  userName?: string,
  userEmail?: string
): boolean {
  const assessment = assessSchoolCustomization(school, userName, userEmail);
  return assessment.level === SchoolCustomizationLevel.BASIC;
}

/**
 * Checks if a school is fully customized
 */
export function isFullyCustomizedSchool(
  school: ISchoolResponse,
  userName?: string,
  userEmail?: string
): boolean {
  const assessment = assessSchoolCustomization(school, userName, userEmail);
  return assessment.level === SchoolCustomizationLevel.FULLY_CUSTOMIZED;
}

/**
 * Gets a user-friendly description of the customization level
 */
export function getCustomizationLevelDescription(level: SchoolCustomizationLevel): string {
  switch (level) {
    case SchoolCustomizationLevel.BASIC:
      return 'Your school is using default settings. Personalize it to make it truly yours!';
    case SchoolCustomizationLevel.PARTIALLY_CUSTOMIZED:
      return 'Great start! Your school has some customization. Consider adding more personal touches.';
    case SchoolCustomizationLevel.FULLY_CUSTOMIZED:
      return 'Excellent! Your school is fully personalized and ready to impress.';
    default:
      return 'School customization status unknown.';
  }
}

/**
 * Gets the next recommended customization step
 */
export function getNextCustomizationStep(assessment: SchoolCustomizationAssessment): string | null {
  if (assessment.recommendations.length === 0) {
    return null;
  }
  
  // Return the first (most important) recommendation
  return assessment.recommendations[0];
}
