/**
 * Utility function to format API error messages
 * Handles both string messages and arrays of validation errors
 * 
 * @param message - The message to format (string, array, or undefined)
 * @returns Formatted string message
 */
export const formatApiMessage = (message: string | any[] | undefined): string => {
  if (Array.isArray(message)) {
    return message.map(m => typeof m === 'object' && m.message ? m.message : String(m)).join(', ');
  }
  return String(message || '');
};