/**
 * RBAC Error Handler Utility
 * Provides consistent error handling and user-friendly messages for role-based access control
 */

import { EUserRole } from '@/config/enums/user';

export interface RBACError {
  code: string;
  message: string;
  statusCode: number;
  userRole?: string;
  attemptedAction?: string;
}

/**
 * RBAC Error Codes as defined in the API documentation
 */
export const RBAC_ERROR_CODES = {
  RBAC_INSUFFICIENT_ROLE: 'RBAC_INSUFFICIENT_ROLE',
  RBAC_SCHOOL_ACCESS_DENIED: 'RBAC_SCHOOL_ACCESS_DENIED',
  RBAC_SCHOOL_MANAGER_NO_SCHOOL: 'RBAC_SCHOOL_MANAGER_NO_SCHOOL',
  RBAC_SCHOOL_USERS_ONLY: 'RBAC_SCHOOL_USERS_ONLY',
  RBAC_SCHOOL_UPDATE_ONLY: 'RBAC_SCHOOL_UPDATE_ONLY',
  RBAC_SCHOOL_UPLOAD_ONLY: 'RBAC_SCHOOL_UPLOAD_ONLY',
  RBAC_CANNOT_CREATE_ADMIN: 'RBAC_CANNOT_CREATE_ADMIN',
  RBAC_CANNOT_TRANSFER_USERS: 'RBAC_CANNOT_TRANSFER_USERS',
  RBAC_ADMIN_ONLY: 'RBAC_ADMIN_ONLY',
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID_TOKEN: 'AUTH_INVALID_TOKEN'
} as const;

/**
 * Get user-friendly error message for INDEPENDENT_TEACHER role restrictions
 */
export function getIndependentTeacherErrorMessage(attemptedAction: string): string {
  const baseMessage = "As an Independent Teacher, you don't have permission to";
  
  switch (attemptedAction.toLowerCase()) {
    case 'create_user':
    case 'user_management':
      return `${baseMessage} manage other users. You can only manage your own profile and school.`;
    case 'teacher_management':
      return `${baseMessage} manage other teachers. You can create and manage your own school instead.`;
    case 'view_users':
      return `${baseMessage} view other users' information. This action is restricted to administrators and school managers.`;
    case 'edit_user':
      return `${baseMessage} edit other users' profiles. You can only edit your own profile.`;
    case 'delete_user':
      return `${baseMessage} delete user accounts. This action is restricted to administrators.`;
    default:
      return `${baseMessage} perform this action. Please contact your administrator if you need assistance.`;
  }
}

/**
 * Get user-friendly error message based on error code and user role
 */
export function getRBACErrorMessage(
  errorCode: string, 
  userRole?: string, 
  attemptedAction?: string
): string {
  // Handle INDEPENDENT_TEACHER specific errors
  if (userRole === EUserRole.INDEPENDENT_TEACHER) {
    switch (errorCode) {
      case RBAC_ERROR_CODES.RBAC_INSUFFICIENT_ROLE:
      case RBAC_ERROR_CODES.RBAC_ADMIN_ONLY:
        return getIndependentTeacherErrorMessage(attemptedAction || 'unknown');
      case RBAC_ERROR_CODES.RBAC_SCHOOL_ACCESS_DENIED:
        return "You can only access your own school's information.";
      case RBAC_ERROR_CODES.RBAC_CANNOT_CREATE_ADMIN:
        return "Independent Teachers cannot create administrator accounts.";
      default:
        return getIndependentTeacherErrorMessage(attemptedAction || 'unknown');
    }
  }

  // Handle general RBAC errors
  switch (errorCode) {
    case RBAC_ERROR_CODES.RBAC_INSUFFICIENT_ROLE:
      return "You don't have the required permissions to perform this action.";
    case RBAC_ERROR_CODES.RBAC_ADMIN_ONLY:
      return "This action is restricted to administrators only.";
    case RBAC_ERROR_CODES.RBAC_SCHOOL_ACCESS_DENIED:
      return "You don't have access to this school's information.";
    case RBAC_ERROR_CODES.RBAC_SCHOOL_MANAGER_NO_SCHOOL:
      return "School managers must be assigned to a school to perform this action.";
    case RBAC_ERROR_CODES.AUTH_REQUIRED:
      return "You must be signed in to perform this action.";
    case RBAC_ERROR_CODES.AUTH_INVALID_TOKEN:
      return "Your session has expired. Please sign in again.";
    default:
      return "You don't have permission to perform this action. Please contact your administrator.";
  }
}

/**
 * Check if a user role has permission for a specific action
 */
export function hasPermission(
  userRole: string, 
  action: 'user_management' | 'teacher_management' | 'school_management' | 'worksheet_management'
): boolean {
  switch (action) {
    case 'user_management':
      return userRole === EUserRole.ADMIN;
    case 'teacher_management':
      return userRole === EUserRole.SCHOOL_MANAGER || userRole === EUserRole.ADMIN;
    case 'school_management':
      return userRole === EUserRole.ADMIN || 
             userRole === EUserRole.SCHOOL_MANAGER || 
             userRole === EUserRole.INDEPENDENT_TEACHER;
    case 'worksheet_management':
      return userRole === EUserRole.TEACHER || 
             userRole === EUserRole.SCHOOL_MANAGER || 
             userRole === EUserRole.INDEPENDENT_TEACHER ||
             userRole === EUserRole.ADMIN;
    default:
      return false;
  }
}

/**
 * Log RBAC violation for security monitoring
 */
export function logRBACViolation(violation: {
  userId?: string;
  userRole?: string;
  attemptedAction: string;
  resource?: string;
  timestamp?: string;
  ip?: string;
  userAgent?: string;
}) {
  const logData = {
    timestamp: violation.timestamp || new Date().toISOString(),
    event: 'RBAC_VIOLATION',
    severity: 'WARNING',
    ...violation
  };
  
  // In production, this should be sent to a proper logging service
  console.warn('🚨 RBAC Violation:', JSON.stringify(logData, null, 2));
  
  // TODO: In production, send to logging service like DataDog, CloudWatch, etc.
  // await sendToLoggingService(logData);
}
