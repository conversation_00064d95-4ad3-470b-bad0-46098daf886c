'use client';

import * as React from 'react';
import { cn } from '@/utils/cn';
import { cva, type VariantProps } from 'class-variance-authority';

export interface TagProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof tagVariants> {
  children: React.ReactNode;
}

const tagVariants = cva(
  'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
  {
    variants: {
      variant: {
        default: 'bg-gray-100 text-gray-800',
        primary: 'bg-blue-100 text-blue-800',
        secondary: 'bg-gray-100 text-gray-800',
        success: 'bg-green-100 text-green-800',
        warning: 'bg-yellow-100 text-yellow-800',
        error: 'bg-red-100 text-red-800',
        info: 'bg-blue-100 text-blue-800',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export const Tag: React.FC<TagProps> = ({
  className,
  variant,
  children,
  ...props
}) => {
  return (
    <span
      className={cn(tagVariants({ variant }), className)}
      {...props}
    >
      {children}
    </span>
  );
};
