import clsx from 'clsx';
import { <PERSON><PERSON>ventHandler } from 'react';

import Image from 'next/image';
import SvgInline from '../SvgInline/SvgInline';
import { LucideIcon, LucideIconVariant } from './LucideIcon';

// Add icon name here to render icon (existing SVG icons)
export const iconVariantTypes = [
  'logo',
  'plus',
  'dashboard-icon',
  'china-flag',
  'united-kingdom-flag',
  'ai-thinking-loader-with-stars',
  'print',
  // Legacy icons that we'll migrate to Lucide
  'chevron-down',
  'eye',
  'eye-slash',
  'lightbulb',
  'check-circle',
  'x-circle',
  'refresh-ccw',
  'refresh',
  'alert-triangle',
  'edit',
  'pen',
  'file-text',
  'target',
  'book-open',
  'graduation-cap',
  'trending-up',
  'globe',
  'hash',
  'list',
  'trash',
  'check',
  'arrow-up',
  'arrow-down',
  'star',
  'clipboard-list',
  'help-circle',
  'x',
  'trash-2',
  'arrow-left',
  'upload',
  'download',
  'replace',
  'presentation',
  'building-2',
  'map-pin',
  'phone',
  'mail',
  'arrow-right',
] as const;

export type IconVariantTypes = (typeof iconVariantTypes)[number];

const themeSpacingValue = 4;

export interface IconProps {
  /**
   * The variant of the icon.
   */
  variant?: IconVariantTypes;

  /**
   * Background variant of the icon.
   * Defaults to none.
   */
  backgroundVariant?: 'primary';

  /**
   * The size of the icon.
   * Used to override the default size of the icon.
   * If not provided, the default size of the icon will be used.
   */
  size?: number;

  /**
   * Whether the icon should be colored dynamically.
   * If true, the icon will be colored according to the parent component.
   */
  dynamicColor?: boolean;

  /**
   * Optional class name of the icon.
   */
  className?: React.HTMLAttributes<HTMLDivElement>['className'];

  /**
   * The color to be applied to the icon.
   */
  color?: string;

  /**
   * Optional click handler of the icon.
   */
  onClick?: MouseEventHandler<HTMLDivElement>;
}

interface VariantData {
  src: string;
  alt: string;
  width: number;
  height: number;
}

// Icons that should use Lucide instead of SVG files
const lucideIcons: LucideIconVariant[] = [
  'arrow-left',
  'chevron-down',
  'eye',
  'eye-slash',
  'lightbulb',
  'check-circle',
  'x-circle',
  'refresh-ccw',
  'refresh',
  'alert-triangle',
  'edit',
  'pen',
  'file-text',
  'target',
  'book-open',
  'graduation-cap',
  'trending-up',
  'globe',
  'hash',
  'list',
  'trash',
  'check',
  'arrow-up',
  'arrow-down',
  'star',
  'clipboard-list',
  'help-circle',
  'x',
  'trash-2',
  'upload',
  'download',
  'replace',
  'presentation',
  'building-2',
  'map-pin',
  'phone',
  'mail',
  'arrow-right',
];

const Icon = ({
  variant,
  backgroundVariant,
  size = 16,
  dynamicColor,
  onClick,
  className,
  ...otherProps
}: IconProps) => {
  const calcSize = (defaultIconSize: number = 2) => ({
    width: size
      ? size * themeSpacingValue
      : defaultIconSize * themeSpacingValue,
    height: size
      ? size * themeSpacingValue
      : defaultIconSize * themeSpacingValue,
  });

  const getIconData = (variantType: IconVariantTypes): VariantData => {
    if (variantType) {
      return {
        src: `/assets/icons/${variantType}.svg`,
        alt: variantType.replace(/-/g, ''),
        ...calcSize(),
      };
    }
    return {
      src: '',
      alt: '',
      width: 0,
      height: 0,
    };
  };

  if (!variant) {
    console.warn('Icon: Missing variant prop');
    return null;
  }

  // Check if this icon should use Lucide
  if (lucideIcons.includes(variant as LucideIconVariant)) {
    return (
      <div
        className={clsx(
          'flex',
          {
            'justify-center items-center flex-shrink-0': backgroundVariant,
            'w-16 h-16 rounded-full bg-neutral-400/40':
              backgroundVariant === 'primary',
            'inline-flex': !!onClick,
          },
          className
        )}
        {...(onClick && { onClick })}
      >
        <LucideIcon
          variant={variant as LucideIconVariant}
          size={size * themeSpacingValue}
          {...otherProps}
        />
      </div>
    );
  }

  // Use original SVG icon logic for legacy icons
  const iconData = getIconData(variant);

  return (
    <div
      className={clsx(
        'flex',
        {
          'justify-center items-center flex-shrink-0': backgroundVariant,
          'w-16 h-16 rounded-full bg-neutral-400/40':
            backgroundVariant === 'primary',
          'inline-flex': !!onClick,
        },
        className
      )}
      {...(onClick && { onClick })}
    >
      {dynamicColor ? (
        <SvgInline {...iconData} {...otherProps} />
      ) : (
        <Image {...iconData} style={{ objectFit: 'contain' }} alt={iconData.alt || `${variant} icon`} {...otherProps} />
      )}
    </div>
  );
};

export default Icon;
