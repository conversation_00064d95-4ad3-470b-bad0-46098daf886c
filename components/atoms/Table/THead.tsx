import type { ComponentPropsWithRef, ElementType } from 'react';
import { forwardRef } from 'react';

import { cn } from '@/utils/cn';

export interface THeadProps extends ComponentPropsWithRef<'thead'> {
  asElement?: ElementType;
}

const THead = forwardRef<HTMLElement, THeadProps>((props, ref) => {
  const {
    asElement: Component = 'thead',
    children,
    className,
    ...rest
  } = props;

  const tHeadClass = cn(Component !== 'thead' && 'thead', className);

  return (
    <Component className={tHeadClass} {...rest} ref={ref}>
      {children}
    </Component>
  );
});

THead.displayName = 'THead';

export default THead;
