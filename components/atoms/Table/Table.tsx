
import type { ComponentPropsWithRef } from 'react';
import { forwardRef } from 'react';

import { cn } from '@/utils/cn';

export interface TableProps extends ComponentPropsWithRef<'table'> {
  asElement?: string | React.ComponentType<any>;
  borderlessRow?: boolean;
  compact?: boolean;
  hoverable?: boolean;
  overflow?: boolean;
  wrapperClass?: string;
}

const Table = forwardRef<HTMLElement, TableProps>((props, ref) => {
  const {
    asElement: Component = 'table',
    borderlessRow,
    children,
    className,
    compact = false,
    hoverable = true,
    overflow = false,
    wrapperClass = '',
    ...rest
  } = props;

  const tableClass = cn(
    'table',
    Component === 'table' ? 'table-default' : 'table-flex',
    hoverable && 'table-hover',
    compact && 'table-compact',
    borderlessRow && 'borderless-row',
    className
  );

  return (
    <div
      className={cn(wrapperClass, {
        'overflow-x-auto overflow-y-hidden': overflow,
      })}
    >
      <Component className={tableClass} {...rest} ref={ref}>
        {children}
      </Component>
    </div>
  );
});

Table.displayName = 'Table';

export default Table;
