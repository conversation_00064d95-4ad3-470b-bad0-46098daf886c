import type { ComponentPropsWithRef, ElementType } from 'react';
import { forwardRef } from 'react';

import { cn } from '@/utils/cn';

export interface TdProps extends ComponentPropsWithRef<'td'> {
  asElement?: ElementType;
}

const Td = forwardRef<HTMLElement, TdProps>((props, ref) => {
  const { asElement: Component = 'td', children, className, ...rest } = props;

  const tdClass = cn(Component !== 'td' && 'td', className);

  return (
    <Component ref={ref} className={tdClass} {...rest}>
      {children}
    </Component>
  );
});

Td.displayName = 'Td';

export default Td;
