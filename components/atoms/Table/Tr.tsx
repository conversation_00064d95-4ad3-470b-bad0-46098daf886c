import type { ComponentPropsWithRef, ElementType } from 'react';
import { forwardRef } from 'react';

import { cn } from '@/utils/cn';

export interface TrProps extends ComponentPropsWithRef<'tr'> {
  asElement?: ElementType;
}

const Tr = forwardRef<HTMLElement, TrProps>((props, ref) => {
  const { asElement: Component = 'tr', children, className, ...rest } = props;

  const trClass = cn(Component !== 'tr' && 'tr', 'cursor-pointer', className);

  return (
    <Component ref={ref} className={trClass} {...rest}>
      {children}
    </Component>
  );
});

Tr.displayName = 'Tr';

export default Tr;
