'use client';

import { useState } from 'react';
import ReactInlineSvg, { Props as ReactInlineSvgProps } from 'react-inlinesvg';

import { motion } from 'framer-motion';

interface SvgInlineProps extends Partial<ReactInlineSvgProps> {
  src: string;
}

const SvgInline = ({ src = '', onLoad, ...props }: SvgInlineProps) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  // image load complete callback
  const localOnLoadingComplete = (svgSrc: string, hasCache: boolean) => {
    setImageLoaded(true);
    onLoad?.(svgSrc, hasCache);
  };

  return (
    <motion.div
      style={{ display: 'inline-flex' }}
      initial={{ opacity: 0 }}
      animate={{ opacity: imageLoaded ? 1 : 0 }}
    >
      <ReactInlineSvg src={src} onLoad={localOnLoadingComplete} {...props} />
    </motion.div>
  );
};

export default SvgInline;
