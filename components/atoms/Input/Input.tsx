'use client';
import { cn } from '@/utils/cn';
import * as React from 'react';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  hasLeftIcon?: boolean;
  hasRightIcon?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, hasLeftIcon = false, hasRightIcon = false, ...props }, ref) => {
    return (
      <input
        ref={ref}
        data-slot="input"
        className={cn(
          'w-full h-full rounded-md border-stroke border border-gray-300 shadow-input',
          'focus:border-stroke focus:border focus:border-gray-300',
          'disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50',
          // Dynamic padding based on icon presence
          {
            'pl-10 pr-4 py-3': hasLeftIcon && !hasRightIcon,
            'pl-4 pr-10 py-3': !hasLeftIcon && hasRightIcon,
            'pl-10 pr-10 py-3': hasLeftIcon && hasRightIcon,
            'px-4 py-3': !hasLeftIcon && !hasRightIcon,
          },
          className
        )}
        {...props}
      />
    );
  }
);

Input.displayName = 'Input';

export { Input };

