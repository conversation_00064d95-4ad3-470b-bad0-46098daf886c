'use client';

import React from 'react';
import { EUserRole } from '@/config/enums/user';

export interface RoleBadgeProps {
  role: EUserRole;
  className?: string;
}

export const RoleBadge: React.FC<RoleBadgeProps> = ({ role, className = '' }) => {
  const getRoleStyles = () => {
    switch (role) {
      case EUserRole.ADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case EUserRole.TEACHER:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case EUserRole.SCHOOL_MANAGER:
        return 'bg-green-100 text-green-800 border-green-200';
      case EUserRole.STUDENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium border ${getRoleStyles()} ${className}`}>
      {role}
    </span>
  );
};