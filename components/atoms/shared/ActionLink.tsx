'use client';

import * as React from 'react';
import Link from 'next/link';
import { cn } from '@/utils/cn';

export interface ActionLinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'danger';
}

export const ActionLink: React.FC<ActionLinkProps> = ({
  href,
  children,
  className,
  variant = 'default',
  ...props
}) => {
  const variantClasses = {
    default: 'text-blue-600 hover:text-blue-800',
    primary: 'text-blue-600 hover:text-blue-800',
    secondary: 'text-gray-600 hover:text-gray-800',
    danger: 'text-red-600 hover:text-red-800',
  };

  return (
    <Link
      href={href}
      className={cn(
        'inline-flex items-center text-sm font-medium underline-offset-4 hover:underline',
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </Link>
  );
};
