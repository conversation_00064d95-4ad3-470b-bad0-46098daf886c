import { ElementType, forwardRef } from 'react';

import { cn } from '@/utils/cn';

export type ContainerVariant = 'default' | 'narrow' | 'wide' | 'full';

interface ContainerProps extends React.ComponentProps<'div'> {
  asElement?: ElementType;
  variant?: ContainerVariant;
}

const Container = forwardRef((props: ContainerProps, ref) => {
  const { 
    className, 
    children, 
    asElement: Component = 'div', 
    variant = 'default',
    ...rest 
  } = props;

  return (
    <Component
      ref={ref}
      className={cn(
        'container mx-auto pt-6',
        {
          'max-w-7xl': variant === 'default',
          'max-w-5xl': variant === 'narrow',
          'max-w-screen-2xl': variant === 'wide',
          'max-w-none': variant === 'full',
        },
        className
      )}
      {...rest}
    >
      {children}
    </Component>
  );
});

Container.displayName = 'Container';

export default Container;
