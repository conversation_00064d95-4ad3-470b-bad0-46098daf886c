import React from 'react';
import Image from 'next/image';
import { cn } from '@/utils/cn';

interface StyledImageProps extends React.ComponentProps<typeof Image> {
  className?: string;
}

export const StyledImage: React.FC<StyledImageProps> = ({ className, alt = '', ...props }) => {
  return (
    <Image
      className={cn(
        'transform scale-100 transition-transform duration-300 hover:scale-105',
        className
      )}
      alt={alt}
      {...props}
    />
  );
};

interface StyledImageWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export const StyledImageWrapper: React.FC<StyledImageWrapperProps> = ({
  children,
  className
}) => {
  return (
    <div
      className={cn(
        'w-full h-full relative flex flex-col',
        className
      )}
    >
      {children}
    </div>
  );
};
