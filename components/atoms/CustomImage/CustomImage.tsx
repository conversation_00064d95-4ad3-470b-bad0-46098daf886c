'use client';
import { ImageProps } from 'next/image';
import React, { useState } from 'react';
import Image from 'next/image';

type TCustomImage = {
  containerProps?: React.HTMLAttributes<HTMLDivElement>;
  src: string;
  alt: string;
  style?: React.CSSProperties;
} & ImageProps;

const IMAGE_ERROR = '/assets/placeholder.png';
// const LOADING_IMAGE = '/assets/loading.jpg';

export const CustomImage: React.FC<TCustomImage> = ({
  containerProps,
  alt = 'img',
  src,
  ...restProps
}) => {
  const [srcImg, setSrc] = useState(src);

  return (
    <div
      className="CustomImage__container relative flex items-center justify-center overflow-hidden w-full h-full"
      {...containerProps}
    >
      <Image
        src={srcImg}
        onError={() => setSrc(IMAGE_ERROR)}
        fill
        loading="lazy"
        alt={alt}
        style={{ objectFit: 'cover' }}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        {...restProps}
      />
    </div>
  );
};
