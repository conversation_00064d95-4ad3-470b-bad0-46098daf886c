import React from 'react';
import { cn } from '@/utils/cn';

export interface Step {
  label: string; // Label for the step
}

export interface StepsProps {
  steps: Step[]; // Array of steps
  currentStep: number; // Current active step (1-based index)
  className?: string; // Additional class names for the steps container
}

const Steps: React.FC<StepsProps> = ({ steps, currentStep, className }) => {
  return (
    <ul className={cn('steps', className)}>
      {steps.map((step, index) => {
        const isCompleted = index <= currentStep; // Steps before the current step are completed
        const isActive = index === currentStep; // Current step is active

        return (
          <li
            key={`step-${index}-${step.label}`}
            className={cn(
              'step',
              isCompleted && 'step-primary',
              isActive && 'step-active' // Active step
            )}
          >
            <span className="text-secondary-content text-[1rem] font-normal">
              {step.label}
            </span>
          </li>
        );
      })}
    </ul>
  );
};

export default Steps;
