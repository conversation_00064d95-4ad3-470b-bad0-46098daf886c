import { UserMenuDropdownProps } from '@/components/molecules/UserMenuDropdown/UserMenuDropdown';
import Sidebar from '@/components/organisms/Sidebar/Sidebar';
import { cn } from '@/utils/cn';
import type { ComponentProps } from 'react';
import type { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse

export interface PageContainerProps extends ComponentProps<'div'> {
  pageContainerType?: 'default' | 'gutterless' | 'contained';
  sidebarItems: { label: string; href: string }[];
  children: React.ReactNode;
  userMenuDropdownProps: UserMenuDropdownProps;
  schoolInfo?: ISchoolResponse | null; // Changed to ISchoolResponse
}

const DashboardTemplate = ({
  pageContainerType = 'default',
  children,
  sidebarItems = [],
  userMenuDropdownProps,
  schoolInfo,
}: PageContainerProps) => {
  const isContained = pageContainerType === 'contained';
  const isAuth = sidebarItems.length > 0;

  return (
    <div className="flex h-screen">
      {/* Sidebar with user dropdown at bottom */}
      {isAuth && <Sidebar items={sidebarItems} userMenuDropdownProps={userMenuDropdownProps} schoolInfo={schoolInfo} />}

      {/* Main Content */}
      <div className="flex-col flex max-h-screen overflow-auto flex-auto justify-between">
        <main className="h-full">
          <div
            className={cn(
              'page-container flex-col flex h-full flex-auto relative bg-slate-50', // Added bg-slate-50 for consistent content background
              pageContainerType !== 'gutterless' && isAuth ? 'pt-6 pb-8.5 px-6' : '', // Added pt-6 for top padding
              isContained && 'container mx-auto'
            )}
          >
            {isContained ? (
              <div
                className={cn(
                  'container mx-auto overflow-auto ',
                  isAuth ? 'max-h-[calc(100%-72px)]' : ''
                )}
              >
                {children}
              </div>
            ) : (
              children
            )}
          </div>
        </main>
        {/* {footer && <Footer pageContainerType={pageContainerType} />} */}
      </div>
    </div>
  );
};

export default DashboardTemplate;
