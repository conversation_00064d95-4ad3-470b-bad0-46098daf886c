import React from 'react';
import { Breadcrumb, BreadcrumbItem } from '@/components/atoms';

export interface DetailTemplateProps {
  header?: React.ReactNode;
  content: React.ReactNode;
  error?: React.ReactNode;
  loading?: React.ReactNode;
  breadcrumbItems?: BreadcrumbItem[];
}

export const DetailTemplate: React.FC<DetailTemplateProps> = ({
  header,
  content,
  error,
  loading,
  breadcrumbItems = [],
}) => {
  return (
    <div className="flex flex-col w-full h-full gap-2">
      {breadcrumbItems.length > 0 && (
        <div className="mb-3">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      )}
      {header}
      {error}
      {loading}
      {content}
    </div>
  );
};