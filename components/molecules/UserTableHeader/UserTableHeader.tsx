'use client';

import React from 'react';
import { Users, Filter, Download } from 'lucide-react';
import { UserTableSearchBar } from '@/components/molecules/UserTableSearchBar/UserTableSearchBar';

export interface UserTableHeaderProps {
  title: string;
  subtitle: string;
  searchTerm: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  onToggleFilterPanel: () => void;
  isFilterPanelOpen: boolean;
  hasActiveFilters: boolean;
  activeFiltersCount: number;
  onExport?: () => void;
}

export const UserTableHeader: React.FC<UserTableHeaderProps> = ({
  title,
  subtitle,
  searchTerm,
  onSearchChange,
  onClearSearch,
  onToggleFilterPanel,
  isFilterPanelOpen,
  hasActiveFilters,
  activeFiltersCount,
  onExport
}) => {
  return (
    <div className="bg-white border-b border-gray-200 p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"> {/* Changed p-5 to p-4 */}
      <div>
        <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
          <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
            <Users size={20} />
          </div>
          {title}
        </h3>
        <p className="text-sm text-gray-600 mt-1.5">
          {subtitle}
        </p>
      </div>
      <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
        <UserTableSearchBar 
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
          onClearSearch={onClearSearch}
        />
        <button
          onClick={onToggleFilterPanel}
          className={`px-3 py-2.5 rounded-lg border shadow-sm flex items-center gap-2 text-sm font-medium transition-all duration-200 ${
            isFilterPanelOpen || hasActiveFilters
              ? 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
          aria-expanded={isFilterPanelOpen}
          aria-controls="filter-panel"
        >
          <Filter size={18} />
          Filters
          {hasActiveFilters && (
            <span className="flex items-center justify-center w-5 h-5 bg-blue-100 text-blue-800 rounded-full text-xs font-bold">
              {activeFiltersCount}
            </span>
          )}
        </button>
        {onExport && (
          <button
            onClick={onExport}
            className="px-3 py-2.5 rounded-lg border border-gray-300 bg-white text-gray-700 shadow-sm flex items-center gap-2 text-sm font-medium hover:bg-gray-50 transition-all duration-200"
            aria-label="Export users"
          >
            <Download size={18} />
            Export
          </button>
        )}
      </div>
    </div>
  );
};
