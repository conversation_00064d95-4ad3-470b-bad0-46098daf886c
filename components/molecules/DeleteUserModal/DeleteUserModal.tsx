'use client';

import React, { useState } from 'react';
import { handleDeleteUserAction } from '@/actions/user.action';
import { Loader2, AlertCircle, UserCircle, Trash, X } from 'lucide-react';
import { EUserRole } from '@/config/enums/user';

export interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    id: string;
    name: string;
    email: string;
    role: EUserRole;
  };
  onSuccess?: () => void;
}

export const DeleteUserModal: React.FC<DeleteUserModalProps> = ({
  isOpen,
  onClose,
  user,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle delete confirmation
  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await handleDeleteUserAction(user.id);

      if (response.status === 'success') {
        setSuccess('User deleted successfully!');
        // Call onSuccess callback after a short delay
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          }
          onClose();
        }, 2000);
      } else {
        setError(Array.isArray(response.message) ? response.message.join(', ') : response.message);
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-xl shadow-2xl p-0 w-full max-w-sm overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header with simple background */}
        <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-bold flex items-center gap-2">
            <Trash size={18} />
            Delete User
          </h2>
          <button 
            onClick={onClose}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        <div className="p-4">
          {/* Status messages */}
          {error && (
            <div className="mb-3 p-2 bg-red-50 border-l-4 border-red-500 rounded-r-md flex items-start gap-2">
              <div className="p-1 bg-red-100 rounded-full text-red-600">
                <AlertCircle size={16} />
              </div>
              <span className="text-red-700 text-xs">{error}</span>
            </div>
          )}

          {success && (
            <div className="mb-3 p-2 bg-green-50 border-l-4 border-green-500 rounded-r-md flex items-start gap-2">
              <div className="p-1 bg-green-100 rounded-full text-green-600">
                <AlertCircle size={16} className="text-green-600" />
              </div>
              <span className="text-green-700 text-xs">{success}</span>
            </div>
          )}

          {/* Warning message - simplified */}
          <div className="mb-3 p-2 bg-gray-50 border-l-4 border-gray-300 rounded-r-md flex items-center gap-2">
            <AlertCircle size={16} className="text-gray-600" />
            <span className="text-gray-700 text-xs">This action cannot be undone.</span>
          </div>

          {/* User information card - simplified */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4 border border-gray-200 flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white shadow-sm">
              <UserCircle size={20} />
            </div>
            <div>
              <h2 className="text-sm font-semibold text-gray-800">{user.name}</h2>
              <p className="text-xs text-gray-600">{user.email}</p>
              <div className="mt-1">
                {user.role === EUserRole.ADMIN && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                    Admin
                  </span>
                )}
                {user.role === EUserRole.TEACHER && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                    Teacher
                  </span>
                )}
                {user.role === EUserRole.SCHOOL_MANAGER && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                    School Manager
                  </span>
                )}
                {user.role === EUserRole.STUDENT && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                    Student
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2">
            <button
              onClick={onClose}
              className="px-3 py-1.5 rounded-lg border border-gray-300 text-gray-700 text-sm hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className={`px-3 py-1.5 rounded-lg bg-red-600 text-white text-sm hover:bg-red-700 transition-all duration-200 flex items-center gap-1.5 ${isSubmitting ? 'opacity-70 cursor-not-allowed' : 'shadow-sm hover:shadow-md'}`}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={14} className="animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash size={14} />
                  Delete User
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
