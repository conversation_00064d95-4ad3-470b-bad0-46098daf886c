'use client';

import { signOut } from 'next-auth/react';
import { cn } from '@/utils/cn';
import Image from 'next/image';
import Link from 'next/link'; // Added Link import
import { UserCircle, ChevronDown, LogOut, User } from 'lucide-react';

export interface UserMenuDropdownProps {
  userName?: string;
  userEmail?: string;
  className?: string;
}

export function UserMenuDropdown({
  userName,
  userEmail,
  className,
}: UserMenuDropdownProps) {
  return (
    <div className={cn("flex items-center w-full h-full", className)}>
      <div className="dropdown dropdown-top w-full h-full">
        <div 
          className="flex items-center w-full h-full gap-3 cursor-pointer px-4 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200 shadow-sm hover:shadow-md" 
          role="button" 
          tabIndex={0}
        >
          <div className="avatar">
            <div className="w-10 h-10 rounded-full !flex items-center justify-center bg-gray-600 text-white shadow-md">
              <UserCircle size={32} className="flex-shrink-0" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} />
            </div>
          </div>
          <div className="flex flex-col overflow-hidden">
            <span className="text-sm font-semibold truncate text-gray-800">{userName || 'User'}</span>
            <span className="text-xs text-gray-500 truncate">{userEmail || ''}</span>
          </div>
          <ChevronDown size={16} className="text-gray-400 ml-1" />
        </div>
        <ul
          tabIndex={0}
          className="menu dropdown-content bg-white rounded-lg z-10 mb-4 w-[calc(100%+2rem)] -ml-4 p-3 shadow-lg left-0 border border-gray-200 animate-in fade-in duration-200"
        >
          <li className="mb-2">
            <Link href="/profile" className="flex items-center gap-2 rounded-md py-2.5 px-3 hover:bg-blue-50 text-sm font-medium text-gray-700 transition-colors">
              <User size={16} className="text-blue-600" />
              Profile
            </Link>
          </li>
          <div className="divider my-1 h-px bg-gray-100"></div>
          <li
            onClick={() => {
              signOut();
            }}
            className="text-red-600"
          >
            <a className="flex items-center gap-2 rounded-md py-2.5 px-3 hover:bg-red-50 text-sm font-medium transition-colors">
              <LogOut size={16} />
              Sign out
            </a>
          </li>
        </ul>
      </div>
    </div>
  );
}
