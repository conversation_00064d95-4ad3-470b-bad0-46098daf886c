import React from 'react';
import { SchoolInfoCard } from './SchoolInfoCard';
import { ISchoolResponse } from '@/apis/schoolApi';

// Example usage of SchoolInfoCard component
export const SchoolInfoCardExample: React.FC = () => {
  // Example school data with all fields populated
  const fullSchoolData: ISchoolResponse = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Springfield Elementary School',
    address: '123 Main Street, Springfield, IL 62701',
    phoneNumber: '+****************',
    registeredNumber: 'REG-2024-001',
    email: '<EMAIL>',
    admin: {
      id: 'admin-123',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
    },
    brand: {
      id: 'brand-123',
      logo: 'https://example.com/logo.png',
      color: '#3872fa',
      image: 'https://example.com/image.png',
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  // Example school data with minimal fields
  const minimalSchoolData: ISchoolResponse = {
    id: '456e7890-e89b-12d3-a456-426614174001',
    name: 'Minimal School',
    address: '',
    phoneNumber: '',
    registeredNumber: '',
    email: '',
  };

  // Example school data with some optional fields
  const partialSchoolData: ISchoolResponse = {
    id: '789e0123-e89b-12d3-a456-426614174002',
    name: 'Partial Data School',
    address: '456 Oak Avenue, Somewhere, CA 90210',
    phoneNumber: '',
    registeredNumber: 'REG-2024-002',
    email: '<EMAIL>',
  };

  const handleEdit = (schoolId: string) => {
    console.log('Edit school with ID:', schoolId);
    // In a real application, this would typically:
    // - Open a modal with edit form
    // - Navigate to an edit page
    // - Trigger a state update to show edit mode
    alert(`Edit school with ID: ${schoolId}`);
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          SchoolInfoCard Examples
        </h1>

        {/* Full School Data Example */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Full School Data
          </h2>
          <div className="max-w-md">
            <SchoolInfoCard
              school={fullSchoolData}
              onEdit={handleEdit}
            />
          </div>
        </div>

        {/* Minimal School Data Example */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Minimal School Data
          </h2>
          <div className="max-w-md">
            <SchoolInfoCard
              school={minimalSchoolData}
              onEdit={handleEdit}
            />
          </div>
        </div>

        {/* Partial School Data Example */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Partial School Data
          </h2>
          <div className="max-w-md">
            <SchoolInfoCard
              school={partialSchoolData}
              onEdit={handleEdit}
            />
          </div>
        </div>

        {/* Custom Styling Example */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Custom Styling Example
          </h2>
          <div className="max-w-md">
            <SchoolInfoCard
              school={fullSchoolData}
              onEdit={handleEdit}
              className="border-2 border-blue-200 shadow-lg"
            />
          </div>
        </div>

        {/* Responsive Grid Example */}
        <div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Responsive Grid Layout
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <SchoolInfoCard
              school={fullSchoolData}
              onEdit={handleEdit}
            />
            <SchoolInfoCard
              school={partialSchoolData}
              onEdit={handleEdit}
            />
            <SchoolInfoCard
              school={minimalSchoolData}
              onEdit={handleEdit}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SchoolInfoCardExample;
