'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { UserCircle, Eye, Edit, Trash, Shield, GraduationCap, School, User as UserIcon } from 'lucide-react';
import { EUserRole } from '@/config/enums/user';

export interface UserTableRowProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
    status?: 'active' | 'inactive' | 'pending' | 'suspended';
    lastActivity?: string;
  };
  onDeleteClick?: (user: {
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
    status?: 'active' | 'inactive' | 'pending' | 'suspended';
    lastActivity?: string;
  }) => void;
  isSelected?: boolean;
}

export const UserTableRow: React.FC<UserTableRowProps> = ({ user, onDeleteClick, isSelected = false }) => {
  const [isHovered, setIsHovered] = useState(false);

  // Function to get role badge styling based on user role
  const getRoleBadge = () => {
    switch (user.role) {
      case EUserRole.ADMIN:
        return {
          icon: <Shield size={16} className="mr-1.5" />,
          text: 'Admin',
          className: 'bg-gray-200 text-gray-800 border-gray-300'
        };
      case EUserRole.TEACHER:
        return {
          icon: <UserIcon size={16} className="mr-1.5" />,
          text: 'Teacher',
          className: 'bg-gray-200 text-gray-800 border-gray-300'
        };
      case EUserRole.SCHOOL_MANAGER:
        return {
          icon: <School size={16} className="mr-1.5" />,
          text: 'School Manager',
          className: 'bg-gray-200 text-gray-800 border-gray-300'
        };
      case EUserRole.STUDENT:
        return {
          icon: <GraduationCap size={16} className="mr-1.5" />,
          text: 'Student',
          className: 'bg-gray-200 text-gray-800 border-gray-300'
        };
      default:
        return {
          icon: <UserIcon size={16} className="mr-1.5" />,
          text: user.role,
          className: 'bg-gray-200 text-gray-800 border-gray-300'
        };
    }
  };

  const roleBadge = getRoleBadge();

  // Get avatar background based on role
  const getAvatarBackground = () => {
    return 'bg-gray-600';
  };

  return (
    <>
      <tr 
        className={`transition-all duration-300 ${
          isSelected 
            ? 'bg-blue-50 hover:bg-blue-100' 
            : isHovered 
              ? 'bg-gray-100' 
              : 'hover:bg-gray-50'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <td className="py-4 px-5">
          <div className="flex items-center gap-4">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getAvatarBackground()} text-white ${isHovered ? '' : ''}`}>
              <UserCircle size={28} />
            </div>
            <div>
              <span className="font-medium text-gray-800 block text-base">{user.name}</span>
              <span className="text-xs text-gray-500 mt-0.5 inline-block">ID: {user.id.substring(0, 8)}...</span>
            </div>
          </div>
        </td>
        <td className="py-4 px-5">
          <div className="flex flex-col">
            <span className="text-gray-700 font-medium">{user.email}</span>
            <span className="text-xs text-gray-500 mt-0.5">User account</span>
          </div>
        </td>
        <td className="py-4 px-5">
          <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium border ${roleBadge.className}`}>
            {roleBadge.icon}
            {roleBadge.text}
          </span>
        </td>
        <td className="py-4 px-5">
          {user.schoolId ? (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                <School size={16} className="text-gray-600" />
              </div>
              <div className="flex flex-col">
                <span className="text-gray-700 font-medium">{user.schoolId.substring(0, 8)}...</span>
                <span className="text-xs text-gray-500">School ID</span>
              </div>
            </div>
          ) : (
            <span className="text-gray-400 italic px-3 py-1.5 bg-gray-50 rounded-full text-xs">Not assigned</span>
          )}
        </td>
        <td className="py-4 px-5">
          <div className="flex items-center gap-2 justify-end">
            <Link 
              href={`/app/users-management/edit/${user.id}`}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 flex items-center gap-1.5" 
              title="Edit User"
            >
              <Edit size={18} />
              <span className="text-sm font-medium hidden sm:inline">Edit</span>
            </Link>
            <button
              onClick={() => onDeleteClick && onDeleteClick(user)}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 flex items-center gap-1.5" 
              title="Delete User"
            >
              <Trash size={18} />
              <span className="text-sm font-medium hidden sm:inline">Delete</span>
            </button>
          </div>
        </td>
      </tr>
    </>
  );
};
