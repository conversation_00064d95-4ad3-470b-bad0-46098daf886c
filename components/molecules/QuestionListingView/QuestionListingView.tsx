'use client';

import { CustomImage } from '@/components/atoms/CustomImage/CustomImage';
import { cn } from '@/utils/cn';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import React, { useState } from 'react';
import Icon from '@/components/atoms/Icon';
import { FillBlankRenderer } from '@/components/molecules/QuestionRenderer/FillBlankRenderer';

// Define the type for a single question
export type Question = {
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'fill_blank'
    | 'creative_writing'
    | string;
  content: string;
  image?: string | null;
  svgCode?: string;
  imagePrompt?: string | null;
  options: string[];
  answer: string[];
  explain: string;
  prompt?: string; // For creative writing prompts
  subject?: string; // Subject of the question from API response
};

// Define the props type for the component
export type QuestionListingViewProps = {
  questions?: Question[];
  containerClass?: string;
  isHtmlContent?: boolean;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
};

const QuestionListingView: React.FC<QuestionListingViewProps> = ({
  questions,
  containerClass = '',
  isHtmlContent = false,
  worksheetInfo,
}) => {
  return (
    <div className={cn('space-y-8', containerClass)}>
      {/* Worksheet Information - Simple, Compact and Sticky */}
      {worksheetInfo && (
        <div className="sticky-header-offset bg-white p-3 rounded-md border border-gray-200 mb-4 shadow-sm">
          <div className="flex flex-wrap gap-x-6 gap-y-1 text-sm">
            {worksheetInfo.subject && ( // Added Subject display
              <div className="flex items-center">
                <span className="font-medium text-gray-600">Subject:</span>
                <span className="ml-2 text-primary">{worksheetInfo.subject}</span>
              </div>
            )}
            {worksheetInfo.topic && (
              <div className="flex items-center">
                <span className="font-medium text-gray-600">Topic:</span>
                <span className="ml-2 text-primary">{worksheetInfo.topic}</span>
              </div>
            )}
            {worksheetInfo.grade && (
              <div className="flex items-center">
                <span className="font-medium text-gray-600">Grade:</span>
                <span className="ml-2 text-primary">{worksheetInfo.grade}</span>
              </div>
            )}
            {worksheetInfo.level && (
              <div className="flex items-center">
                <span className="font-medium text-gray-600">Level:</span>
                <span className="ml-2 text-primary">{worksheetInfo.level}</span>
              </div>
            )}
            {worksheetInfo.language && (
              <div className="flex items-center">
                <span className="font-medium text-gray-600">Language:</span>
                <span className="ml-2 text-primary">
                  {worksheetInfo.language}
                </span>
              </div>
            )}
            {worksheetInfo.totalQuestions && (
              <div className="flex items-center">
                <span className="font-medium text-gray-600">
                  Total Questions:
                </span>
                <span className="ml-2 text-primary">
                  {worksheetInfo.totalQuestions}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {questions && questions.length > 0 && (
        <div>
          <div className="text-xl text-black font-semibold">
            {questions.length > 1
              ? 'Generated Questions'
              : 'Generated Question'}
          </div>
          <div className="text-gray-500 text-lg font-normal">
            {questions.length}{' '}
            {questions.length > 1 ? 'questions have' : 'question has'} been
            generated so far.
          </div>
        </div>
      )}
      {questions?.map((question, index) => {
        return (
          <div
            key={index}
            className="mb-8 p-6 bg-white border border-gray-200 rounded-xl shadow-lg" // Enhanced card styling
          >
            {/* Question Index, Type and Subject */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-5 pb-3 border-b border-gray-200">
              <span className="text-xl font-semibold text-gray-700 mb-2 sm:mb-0"> 
                Question {index + 1}
              </span>
              <div className="flex items-center gap-3">
                {question.subject && (
                  <span className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {question.subject}
                  </span>
                )}
                <span className="text-xs px-3 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                  {question.type.replace('_', ' ')}
                </span>
              </div>
            </div>

            {/* Question Content */}
            <div className="bg-gray-50 p-5 rounded-lg mb-5"> 
              {isHtmlContent ? (
                <div // Changed h2 to div for better semantic if content is complex HTML
                  className="text-base text-gray-800 leading-relaxed question-content-html" // Added a class for potential specific styling
                  dangerouslySetInnerHTML={{ __html: question.content }}
                />
              ) : (
                <p className="text-base text-gray-800 leading-relaxed">{question.content}</p> // Changed h2 to p
              )}
            </div>

            {/* SVG Illustration or Image (if available) */}
            {question?.image && (
              <div
                className="my-4 w-full max-w-lg"
                dangerouslySetInnerHTML={{ __html: question.image }}
              />
            )}

            {/* Question Type Specific Rendering */}
            {/* Multiple Choice & Single Choice Options */}
            {(question.type === 'multiple_choice' ||
              question.type === 'single_choice') &&
              question?.options?.length > 0 && (
                <div className="space-y-1 mt-5">
                  {question?.options?.map((option, optionIndex) => {
                    const isAnswer = question.answer?.includes(option);
                    const isSingleChoice = question.type === 'single_choice';
                    const inputType = isSingleChoice ? 'radio' : 'checkbox';

                    return (
                      <label
                        key={optionIndex}
                        className="flex items-start space-x-3 cursor-pointer py-3 border border-gray-100 hover:bg-gray-50 rounded-md px-3 mb-2"
                      >
                        <input
                          type={inputType}
                          name={`question-${index}`}
                          value={option}
                          defaultChecked={isAnswer}
                          disabled={!isAnswer}
                          className={cn(
                            inputType === 'radio'
                              ? 'radio radio-primary'
                              : 'checkbox checkbox-primary',
                            'mt-1 border-2',
                            isAnswer ? 'border-primary' : 'border-gray-300'
                          )}
                        />
                        {isHtmlContent ? (
                          <span
                            className={cn(
                              'flex-1 pt-0.5',
                              isAnswer && 'font-medium text-primary'
                            )}
                            dangerouslySetInnerHTML={{ __html: option }}
                          />
                        ) : (
                          <span
                            className={cn(
                              'flex-1 pt-0.5',
                              isAnswer && 'font-medium text-primary'
                            )}
                          >
                            {option}
                          </span>
                        )}
                      </label>
                    );
                  })}
                </div>
              )}

              {/* Fill in the Blank */}
              {question.type === 'fill_blank' && (
                <FillBlankRenderer
                  content={question.content}
                  answer={question.answer}
                  isHtmlContent={isHtmlContent}
                />
              )}

            

            {/* Explanation Accordion */}
            {question?.explain && (
              <ExplanationAccordion
                explanation={question.explain}
                isHtmlContent={isHtmlContent}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

// Explanation Accordion Component
type ExplanationAccordionProps = {
  explanation: string;
  isHtmlContent?: boolean;
};

const ExplanationAccordion: React.FC<ExplanationAccordionProps> = ({
  explanation,
  isHtmlContent = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-2.5 px-4 bg-gray-50 focus:outline-none transition-colors hover:bg-gray-100"
      >
        <div className="flex items-center gap-2">
          <Icon
            variant="chevron-down"
            size={3.5}
            className={cn(
              'transition-transform duration-200 text-primary',
              isOpen && 'rotate-180'
            )}
          />
          <span className="font-medium text-primary text-sm">
            View Explanation
          </span>
        </div>
      </button>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-4 bg-white border-t border-gray-200">
          {isHtmlContent ? (
            <div
              className="text-gray-700 text-sm leading-relaxed max-w-none"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(explanation),
              }}
            />
          ) : (
            <div className="text-gray-700 text-sm leading-relaxed">
              {explanation.split('\n').map((paragraph, i) =>
                paragraph.trim() ? (
                  <p key={i} className="mb-3">
                    {paragraph}
                  </p>
                ) : null
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionListingView;
