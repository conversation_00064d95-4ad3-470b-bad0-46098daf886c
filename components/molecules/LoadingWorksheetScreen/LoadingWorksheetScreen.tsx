'use client';

import { useState } from 'react';
import Icon from "@/components/atoms/Icon";
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ProgressBar, ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { useWorksheetProgress } from '@/hooks/useWorksheetProgress';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';




type LoadingWorksheetScreenProps = {
  worksheetId?: string;
  initialProgress?: ProgressData;
  initialQuestions?: Question[];
  worksheetInfo?: {
    topic?: string;
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
};

export const LoadingWorksheetScreen: React.FC<LoadingWorksheetScreenProps> = ({
  worksheetId,
  initialProgress,
  initialQuestions = [],
  worksheetInfo
}) => {
  const { questions, progress: socketProgress } = useWorksheetProgress({
    worksheetId: worksheetId || '',
    initialStatus: WorksheetGeneratingStatus.PENDING,
    initialQuestions,
    initialProgress,
  });

  // Use the progress from the socket if available, otherwise use the initial progress
  const progress = socketProgress || initialProgress || {
    current: 0,
    total: 10, // Default to 10 total questions until we get real data
    percentage: 0
  };

  return (
    <div className="w-full h-full flex flex-col">

      {/* Content area */}
      <div className="flex-1 overflow-auto p-4 pb-16"> {/* Added padding at bottom for the progress bar */}
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full">
            {questions.length > 0 ? (
              <QuestionListingView
                questions={questions}
                containerClass="pb-20"
                isHtmlContent
                worksheetInfo={worksheetInfo}
              />
            ) : (
              <div className="w-full h-64 flex flex-col items-center justify-center">
                <div className="text-center text-gray-500 mb-4">
                  Questions will appear here as they are generated...
                </div>
                <div className="animate-pulse flex space-x-4 w-full max-w-2xl">
                  <div className="flex-1 space-y-6 py-1">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="space-y-3">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="h-4 bg-gray-200 rounded col-span-2"></div>
                        <div className="h-4 bg-gray-200 rounded col-span-1"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modern AI-style progress indicator at the bottom */}
      <ProgressBar progress={progress} />
    </div>
  );
}