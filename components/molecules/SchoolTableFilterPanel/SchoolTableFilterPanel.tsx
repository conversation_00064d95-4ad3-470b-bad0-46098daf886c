'use client';

import React from 'react';
import { Button } from '@/components/atoms/Button/Button';
import { X } from 'lucide-react';

// Define a simple filter structure for schools, can be expanded later
export interface SchoolFilters {
  // Example: status: 'all' | 'active' | 'inactive';
  // Example: region: string; // if schools have regions
  [key: string]: any; // Allow for dynamic filter properties
}

export interface SchoolTableFilterPanelProps {
  filters: SchoolFilters;
  onFilterChange: (filterName: keyof SchoolFilters, value: any) => void;
  onClearFilters: () => void;
  // Add any school-specific filter options here if needed
  // e.g., schoolTypes?: Array<{ value: string; label: string }>;
}

export const SchoolTableFilterPanel: React.FC<SchoolTableFilterPanelProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
}) => {
  // Check if any filters are active (customize this logic based on actual filters)
  const hasActiveFilters = Object.values(filters).some(value => value !== 'all' && value !== '');

  return (
    <div className="p-4 border-b border-gray-200 bg-gray-50">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {/* Placeholder for actual filter inputs */}
        {/* 
        Example filter input:
        <div>
          <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            id="status-filter"
            name="status"
            value={filters.status || 'all'}
            onChange={(e) => onFilterChange('status', e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div> 
        */}

        {/* If no specific filters are defined yet, show a message or keep it minimal */}
        {!Object.keys(filters).length && (
            <p className="text-sm text-gray-500 col-span-full">No filters currently available.</p>
        )}
      </div>

      {/* Clear Filters Button */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200 flex justify-end">
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="text-sm"
          >
            <X size={16} className="mr-2" />
            Clear All Filters
          </Button>
        </div>
      )}
    </div>
  );
};
