'use client';

import React, { useState } from 'react';
import { handleDeleteWorksheetAction } from '@/actions/worksheet.action';
import { Loader2, AlertCircle, FileText, Trash, X } from 'lucide-react';

export interface DeleteWorksheetModalProps {
  isOpen: boolean;
  onClose: () => void;
  worksheet: {
    id: string;
    title: string;
    // Add other relevant worksheet properties if needed for display
    // e.g., grade?: string; subject?: string;
  };
  onSuccess?: () => void;
}

export const DeleteWorksheetModal: React.FC<DeleteWorksheetModalProps> = ({
  isOpen,
  onClose,
  worksheet,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle delete confirmation
  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Assuming handleDeleteWorksheetAction takes worksheetId and returns a similar response structure
      const response = await handleDeleteWorksheetAction(worksheet.id);

      if (response.status === 'success') {
        setSuccess('Worksheet deleted successfully!');
        // Call onSuccess callback after a short delay
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          }
          onClose(); // Close modal after success and delay
        }, 2000);
      } else {
        // Check for specific "not found" error message
        if (response.message && response.message.includes('not found')) {
          // If worksheet not found, consider it as already deleted
          setSuccess('Worksheet no longer exists. It may have been deleted already.');
          setTimeout(() => {
            if (onSuccess) {
              onSuccess();
            }
            onClose();
          }, 2000);
        } else {
          // Handle other errors
          setError(Array.isArray(response.message) ? response.message.join(', ') : response.message || 'Failed to delete worksheet.');
        }
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred while deleting the worksheet.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          disabled={isSubmitting}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 disabled:opacity-50"
          aria-label="Close"
        >
          <X size={20} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-5">
          <div className="bg-red-100 p-2 rounded-full mr-3">
            <Trash className="text-red-600" size={20} />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">Delete Worksheet</h3>
        </div>

        {/* Content */}
        <div className="mb-6">
          <div className="flex items-start mb-4">
            <div className="bg-blue-100 p-2 rounded-full mr-3">
              <FileText className="text-blue-600" size={20} />
            </div>
            <div>
              <p className="font-medium text-gray-900">{worksheet.title}</p>
              {/* You can add more worksheet details here if needed */}
            </div>
          </div>
          
          <p className="text-gray-600">
            Are you sure you want to delete this worksheet? This action cannot be undone.
          </p>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={18} />
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Success message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
            <div className="text-green-500 mr-2">✓</div>
            <p className="text-green-600 text-sm">{success}</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={isSubmitting || success !== null}
            className="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="animate-spin mr-2" size={16} />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
