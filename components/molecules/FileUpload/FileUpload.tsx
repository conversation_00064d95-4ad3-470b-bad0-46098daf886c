'use client';

import React, { useRef, useState, useCallback } from 'react';
import { Upload, Trash2, Image as ImageIcon, CheckCircle } from 'lucide-react';
import Image from 'next/image';

export interface FileUploadProps {
  id: string;
  accept?: string;
  preview: string | null;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClear: () => void;
  icon?: React.ReactNode;
  label?: string;
  maxSize?: string;
  disabled?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  id,
  accept = 'image/*',
  preview,
  onUpload,
  onClear,
  icon = <Upload size={14} className="text-gray-400" />, // Further reduced icon size to 14
  label = 'Click to upload',
  maxSize = 'MAX. 2MB',
  disabled = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0 && fileInputRef.current) {
      // Set the files to the input element
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(files[0]);
      fileInputRef.current.files = dataTransfer.files;
      
      // Trigger the change event directly
      setIsUploading(true);
      setTimeout(() => {
        if (fileInputRef.current) {
          const changeEvent = {
            target: fileInputRef.current,
            currentTarget: fileInputRef.current,
          } as React.ChangeEvent<HTMLInputElement>;
          onUpload(changeEvent);
        }
        setIsUploading(false);
      }, 500);
    }
  }, [disabled, onUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setIsUploading(true);
    setTimeout(() => {
      onUpload(e);
      setIsUploading(false);
    }, 500);
  }, [onUpload]);

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const baseClasses = `
    relative w-full transition-all duration-200 ease-in-out rounded-xl border-2 border-dashed
    ${disabled 
      ? 'opacity-50 cursor-not-allowed border-gray-200 bg-gray-50' 
      : isDragOver 
        ? 'border-indigo-400 bg-indigo-50 scale-[1.02]' 
        : preview 
          ? 'border-green-300 bg-green-50' 
          : 'border-gray-300 bg-gray-50 hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer'
    }
  `;

  return (
    <div className="space-y-3">
      {/* Preview Mode */}
      {preview && (
        <div className="relative group">
          <div className="relative w-full h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden border-2 border-green-300 shadow-sm"> {/* Reduced height h-32 to h-24 */}
            <Image 
              src={preview} 
              alt="File preview" 
              className="object-contain transition-transform duration-200 group-hover:scale-105"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            
            {/* Success indicator */}
            <div className="absolute top-1.5 left-1.5 bg-green-500 text-white p-1 rounded-full shadow-lg"> {/* Adjusted positioning and padding */}
              <CheckCircle size={12} /> {/* Reduced icon size */}
            </div>

            {/* Remove button */}
            {!disabled && (
              <button
                type="button"
                onClick={onClear}
                className="absolute top-1.5 right-1.5 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-all duration-200 shadow-lg hover:scale-110 opacity-0 group-hover:opacity-100" /* Adjusted positioning and padding */
                aria-label="Remove file"
              >
                <Trash2 size={12} /> {/* Reduced icon size */}
              </button>
            )}

            {/* Overlay with file info */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"> {/* Reduced padding */}
              <p className="text-white text-xs font-medium">Click to change image</p>
            </div>
          </div>

          {/* Change file input (hidden) */}
          <input
            id={`${id}-change`}
            type="file"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            accept={accept}
            onChange={handleFileSelect}
            disabled={disabled}
          />
        </div>
      )}

      {/* Upload Mode */}
      {!preview && (
        <div
          className={baseClasses}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div className="flex flex-col items-center justify-center py-2 px-4"> {/* Reduced py-3 to py-2 */}
            {/* Upload Icon */}
            <div className={`mb-1 p-1 rounded-full transition-all duration-200 ${ /* Reduced mb-2 to mb-1, p-1.5 to p-1 */
              isDragOver 
                ? 'bg-indigo-100 text-indigo-600 scale-110' 
                : 'bg-gray-100 text-gray-400'
            }`}>
              {isUploading ? (
                <div className="animate-spin">
                  <Upload size={14} /> {/* Ensured consistent icon size */}
                </div>
              ) : (
                icon // icon prop already updated to size 14
              )}
            </div>

            {/* Upload Text */}
            <div className="text-center">
              <p className={`text-sm font-medium transition-colors duration-200 ${
                isDragOver ? 'text-indigo-700' : 'text-gray-700'
              }`}>
                {isUploading ? (
                  'Uploading...'
                ) : isDragOver ? (
                  'Drop your file here'
                ) : (
                  <>
                    <span className="text-indigo-600 hover:text-indigo-700 cursor-pointer">
                      {label}
                    </span>
                    <span className="text-gray-500"> or drag and drop</span>
                  </>
                )}
              </p>
              
              {!isUploading && (
                <p className="text-xs text-gray-500"> {/* Removed mt-1 */}
                  PNG, JPG, GIF or SVG ({maxSize})
                </p>
              )}
            </div>

            {/* Progress indicator */}
            {isUploading && (
              <div className="w-full max-w-xs mt-4">
                <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div className="bg-indigo-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              </div>
            )}
          </div>

          {/* Hidden file input */}
          <input
            id={id}
            type="file"
            className="hidden"
            accept={accept}
            ref={fileInputRef}
            onChange={handleFileSelect}
            disabled={disabled}
          />
        </div>
      )}
    </div>
  );
};
