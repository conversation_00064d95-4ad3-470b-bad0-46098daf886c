'use client';

import React from 'react';
import { Search, X } from 'lucide-react';
import { InputWithIcon } from '@/components/molecules/InputWithIcon';

export interface UserTableSearchBarProps {
  searchTerm: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  placeholder?: string;
}

export const UserTableSearchBar: React.FC<UserTableSearchBarProps> = ({
  searchTerm,
  onSearchChange,
  onClearSearch,
  placeholder = 'Search users...'
}) => {
  return (
    <div className="flex-grow sm:w-72">
      <InputWithIcon
        type="text"
        placeholder={placeholder}
        value={searchTerm}
        onChange={onSearchChange}
        leftIcon={<Search size={18} />}
        rightIcon={searchTerm ? <X size={16} /> : undefined}
        onRightIconClick={searchTerm ? onClearSearch : undefined}
        rightIconAriaLabel="Clear search"
        className="text-sm"
        aria-label="Search users"
      />
    </div>
  );
};