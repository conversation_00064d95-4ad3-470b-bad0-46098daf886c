'use client';

import * as React from 'react';
import { cn } from '@/utils/cn';
import { Input, InputProps } from '@/components/atoms/Input/Input';

export interface CheckboxItemProps extends InputProps {
  label: string;
}

function CheckboxItem({ label, className, ...props }: CheckboxItemProps) {
  return (
    <div className="flex items-center gap-2">
      <Input
        type="checkbox"
        className={cn(
          'w-fit h-5 rounded border-gray-300 ',
          'checked:bg-primary checked:border-primary',
          className
        )}
        {...props}
      />
      <label
        htmlFor={props.id}
        className="text-sm font-medium text-grey-28 select-none w-fit"
      >
        {label}
      </label>
    </div>
  );
}

export { CheckboxItem };
