'use client';

import React from 'react';
import { X } from 'lucide-react';
import { EUserRole } from '@/config/enums/user';

export interface UserTableFilterPanelProps {
  filters: {
    role: EUserRole | 'all';
    status: 'active' | 'inactive' | 'pending' | 'suspended' | 'all';
  };
  onFilterChange: (filterName: string, value: any) => void;
  onClearFilters: () => void;
  hideRoleFilter?: boolean; // Add new prop
}

export const UserTableFilterPanel: React.FC<UserTableFilterPanelProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  hideRoleFilter = false // Add default value for new prop
}) => {
  const gridColsClass = hideRoleFilter ? "md:grid-cols-2" : "md:grid-cols-3";

  return (
    <div 
      className={`bg-gray-50 border-b border-gray-200 p-4 grid grid-cols-1 ${gridColsClass} gap-4`}
      style={{ animation: 'fadeIn 0.3s ease-in-out' }}
    >
      {!hideRoleFilter && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
          <select
            value={filters.role}
            onChange={(e) => onFilterChange('role', e.target.value)}
            className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            aria-label="Filter by role"
          >
            <option value="all">All Roles</option>
            <option value={EUserRole.ADMIN}>Admin</option>
            <option value={EUserRole.TEACHER}>Teacher</option>
            <option value={EUserRole.SCHOOL_MANAGER}>School Manager</option>
            <option value={EUserRole.STUDENT}>Student</option>
          </select>
        </div>
      )}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select
          value={filters.status}
          onChange={(e) => onFilterChange('status', e.target.value as any)}
          className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          aria-label="Filter by status"
        >
          <option value="all">All Statuses</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="pending">Pending</option>
          <option value="suspended">Suspended</option>
        </select>
      </div>
      <div className="flex items-end gap-2">
        <button
          onClick={onClearFilters}
          className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-2"
          aria-label="Clear all filters"
        >
          <X size={16} />
          Clear Filters
        </button>
      </div>
    </div>
  );
};
