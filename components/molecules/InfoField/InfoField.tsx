'use client';

import React, { ReactNode } from 'react';
import { LucideIcon } from '@/components/atoms/Icon/LucideIcon';

export interface InfoFieldProps {
  label: string;
  value: string | ReactNode;
  icon?: ReactNode;
  className?: string;
}

export const InfoField: React.FC<InfoFieldProps> = ({
  label,
  value,
  icon,
  className = '',
}) => {
  return (
    <div className={`flex items-center ${className}`}>
      {icon && <div className="mr-2 text-primary">{icon}</div>}
      <div>
        <div className="text-sm opacity-70">{label}</div>
        <div>{value}</div>
      </div>
    </div>
  );
};