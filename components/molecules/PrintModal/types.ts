export interface PrintFormData {
  // Exam Details
  examTitle: string;
  subject: string;
  examDate: string;
  duration: string;

  // Hidden fields (still used in print configuration)
  gradeLevel: string;
  studentName: string;
  className: string;
  includeMarkAllocation: boolean;
  includeInstructions: boolean;
  paperSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
}

export interface ExamHeader {
  schoolName: string;
  gradeLevel: string;
  logoUrl?: string | null;
  examTitle: string;
  subject: string;
  examDate: string;
  duration: string;
  studentName: string;
  className: string;
}

export interface PrintOptions {
  includeMarkAllocation: boolean;
  includeInstructions: boolean;
  paperSize: 'A4' | 'Letter';
  orientation: 'portrait' | 'landscape';
}
