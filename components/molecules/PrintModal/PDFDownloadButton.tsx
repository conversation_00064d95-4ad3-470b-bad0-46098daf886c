'use client';

import React from 'react';
import { Download } from 'lucide-react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { PDFExport } from '@/components/molecules/PDFExport/PDFExport';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { ExamHeader, PrintOptions } from './types';
import { cn } from '@/utils/cn';

interface PDFDownloadButtonProps {
  questions: Question[];
  examHeader: ExamHeader;
  printOptions: PrintOptions;
  fileName: string;
  isLoading: boolean;
  isFormValid?: boolean;
  className?: string;
  onValidationError?: () => void;
}

const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  questions,
  examHeader,
  printOptions,
  fileName,
  isLoading,
  isFormValid = true,
  className,
  onValidationError
}) => {
  // Error handling for PDF generation
  const [error, setError] = React.useState<Error | null>(null);

  if (error) {
    console.error('PDF generation error:', error);
    return (
      <div className={cn(
        "min-w-24 h-10 bg-red-500 text-white rounded-md text-sm flex items-center justify-center gap-2 px-4",
        className
      )}>
        Error loading PDF
      </div>
    );
  }

  // Log props for debugging
  console.log('PDFDownloadButton props:', {
    questionsLength: questions.length,
    examHeader,
    printOptions,
    isFormValid
  });

  try {
    // Check if questions array is empty
    if (!questions || questions.length === 0) {
      console.error('No questions provided to PDFDownloadButton');
      return (
        <div className={cn(
          "min-w-24 h-10 bg-red-500 text-white rounded-md text-sm flex items-center justify-center gap-2 px-4",
          className
        )}>
          No questions to export
        </div>
      );
    }

    return (
      <PDFDownloadLink
        document={
          <PDFExport
            questions={questions}
            examHeader={examHeader}
            printOptions={printOptions}
          />
        }
        fileName={fileName}
        className={cn(
          "min-w-24 h-10 bg-primary hover:bg-primary/90 text-white transition-colors rounded-md text-sm flex items-center justify-center gap-2 px-4",
          (isLoading || !isFormValid) && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={(e) => {
          if (!isFormValid) {
            e.preventDefault();
            if (onValidationError) {
              onValidationError();
            }
          }
        }}
        aria-label="Download PDF"
      >
        {({ loading, error }) => {
          if (error) {
            console.error('PDFDownloadLink error:', error);
            return (
              <>
                <Download size={16} />
                Error generating PDF
              </>
            );
          }
          return (
            <>
              <Download size={16} />
              {loading ? "Generating PDF..." : "Download PDF"}
            </>
          );
        }}
      </PDFDownloadLink>
    );
  } catch (err) {
    console.error('PDFDownloadButton caught error:', err);

    if (err instanceof Error) {
      setError(err);
    } else {
      setError(new Error('Unknown error occurred'));
    }

    return (
      <div className={cn(
        "min-w-24 h-10 bg-red-500 text-white rounded-md text-sm flex items-center justify-center gap-2 px-4",
        className
      )}>
        <Download size={16} />
        Error loading PDF
      </div>
    );
  }
};

export default PDFDownloadButton;
