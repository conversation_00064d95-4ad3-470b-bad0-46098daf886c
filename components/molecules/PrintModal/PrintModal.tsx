'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { Label } from '@/components/atoms/Label/Label';
import Icon from '@/components/atoms/Icon';
import { PrintFormData } from './types';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { cn } from '@/utils/cn';
import {
  X, Info, FileText, Calendar, Clock, Download,
  AlertTriangle, CheckCircle
} from 'lucide-react';

import PDFDownloadButton from './PDFDownloadButton';

interface PrintModalProps {
  isOpen: boolean;
  onClose: () => void;
  questions: Question[];
  worksheetInfo?: {
    topic?: string;
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
}

export const PrintModal: React.FC<PrintModalProps> = ({
  isOpen,
  onClose,
  questions,
  worksheetInfo,
  schoolInfo,
}) => {
  const [isLoading] = useState(false); // Keep isLoading state for PDFDownloadButton
  const [isClient, setIsClient] = useState(false);
  const [notification, setNotification] = useState<{
    message: string;
    type: 'error' | 'warning' | 'success' | 'info';
  } | null>(null);


  useEffect(() => {
    setIsClient(true);
  }, []);

  const [formData, setFormData] = useState<PrintFormData>({
    // Exam Details
    examTitle: '',
    subject: worksheetInfo?.topic || '',
    examDate: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
    duration: '',

    // Hidden fields (still used in print configuration)
    gradeLevel: worksheetInfo?.grade || '',
    studentName: '',
    className: '',
    includeMarkAllocation: true,
    includeInstructions: true,
    paperSize: 'A4',
    orientation: 'portrait',
  });

  const modalRef = useRef<HTMLDialogElement>(null);

  // Open or close the modal based on the isOpen prop
  React.useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.showModal();
    } else if (!isOpen && modalRef.current) {
      modalRef.current.close();
    }
  }, [isOpen]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Logo handling has been removed as school information now comes from session

  // Create exam header from form data and school info - memoized to prevent re-renders
  const examHeader = React.useMemo(() => {
    const header = {
      schoolName: schoolInfo?.name || '',
      gradeLevel: formData.gradeLevel,
      logoUrl: schoolInfo?.logoUrl || null, // Use original logo URL directly
      examTitle: formData.examTitle,
      subject: formData.subject,
      examDate: formData.examDate,
      duration: formData.duration,
      studentName: formData.studentName,
      className: formData.className,
    };

    return header;
  }, [formData, schoolInfo]);

  // Create print options from form data - memoized to prevent re-renders
  const printOptions = React.useMemo(() => {
    return {
      includeMarkAllocation: formData.includeMarkAllocation,
      includeInstructions: formData.includeInstructions,
      paperSize: formData.paperSize,
      orientation: formData.orientation,
    };
  }, [formData]);

  // Check if form is valid
  const isFormValid: boolean = !!(formData.examTitle && formData.subject);

  // Tooltip component
  const Tooltip = ({ text, children }: { text: string; children: React.ReactNode }) => {
    return (
      <div className="relative group">
        {children}
        <div className="absolute left-0 bottom-full mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
          <div className="bg-gray-800 text-white text-sm rounded py-1.5 px-3 whitespace-nowrap">
            {text}
            <div className="absolute top-full left-3 w-2 h-2 bg-gray-800 transform rotate-45"></div>
          </div>
        </div>
      </div>
    );
  };

  // Notification component
  const Notification = ({
    message,
    type = 'error',
    onClose
  }: {
    message: string;
    type?: 'error' | 'warning' | 'success' | 'info';
    onClose?: () => void;
  }) => {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose?.();
      }, 5000);

      return () => clearTimeout(timer);
    }, [onClose]);

    if (!isVisible) return null;

    const iconMap = {
      error: <AlertTriangle className="h-5 w-5" />,
      warning: <AlertTriangle className="h-5 w-5" />,
      success: <CheckCircle className="h-5 w-5" />,
      info: <Info className="h-5 w-5" />
    };

    const colorMap = {
      error: 'bg-red-50 text-red-600 border-red-500',
      warning: 'bg-amber-50 text-amber-600 border-amber-500',
      success: 'bg-green-50 text-green-600 border-green-500',
      info: 'bg-blue-50 text-blue-600 border-blue-500'
    };

    return (
      <div className={`fixed bottom-4 right-4 z-[9999] max-w-md`}
        style={{
          animation: 'slideInUp 0.3s ease-out forwards'
        }}
      >
        <style jsx>{`
          @keyframes slideInUp {
            from {
              transform: translateY(20px);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
        `}</style>
        <div className={`flex items-center p-4 rounded-lg shadow-lg border-l-4 ${colorMap[type]} bg-white`}>
          <div className="mr-3 flex-shrink-0">
            {iconMap[type]}
          </div>
          <div className="flex-1 mr-2">
            <p className="text-sm font-medium">{message}</p>
          </div>
          <button
            onClick={() => {
              setIsVisible(false);
              onClose?.();
            }}
            className="flex-shrink-0 ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex items-center justify-center h-8 w-8 hover:bg-gray-200 hover:bg-opacity-20 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}
      <dialog
        id="print_modal"
        className="modal modal-bottom sm:modal-middle"
        ref={modalRef}
        aria-labelledby="print-modal-title"
      >
        <div className="modal-box w-11/12 max-w-2xl bg-white p-0 rounded-xl shadow-2xl overflow-hidden relative">
          {/* Header with colored background */}
          <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
            <h2 id="print-modal-title" className="text-lg font-bold flex items-center gap-2">
              <FileText size={18} />
              Print Worksheet
            </h2>
            <button 
              onClick={onClose}
              className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
              aria-label="Close"
            >
              <X size={16} className="text-white" />
            </button>
          </div>

          <div className="p-4">
            <p className="text-gray-500 mb-3 text-sm">Configure your worksheet before printing</p>

            <div className="space-y-3">
              {/* School information is now automatically retrieved from the user's session */}

              {/* Exam Details Section */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="font-medium text-base mb-2 text-gray-700 flex items-center">
                  <span className="bg-primary/10 text-primary p-1 rounded-md mr-2 flex items-center justify-center">
                    <Icon variant="file-text" size={5} />
                  </span>
                  <span className="text-base font-semibold">Exam Details</span>
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="form-control w-full">
                    <Label className="text-gray-700 flex items-center mb-1 text-sm">
                      <span className="font-medium">Exam Title</span> <span className="text-error font-medium ml-1">*</span>
                      <Tooltip text="Enter the title of this assessment">
                        <Info size={16} className="ml-1 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <Input
                      name="examTitle"
                      value={formData.examTitle}
                      onChange={handleInputChange}
                      placeholder="e.g., Mid-Term Assessment"
                      required
                      className="focus:outline-none focus:ring-0 border-0 bg-white transition-all h-10 py-2 text-base"
                      aria-required="true"
                    />
                    {!formData.examTitle && (
                      <p className="text-sm text-error mt-1">This field is required</p>
                    )}
                  </div>

                  <div className="form-control w-full">
                    <Label className="text-gray-700 flex items-center mb-1 text-sm">
                      <span className="font-medium">Subject</span> <span className="text-error font-medium ml-1">*</span>
                      <Tooltip text="Enter the subject for this worksheet">
                        <Info size={16} className="ml-1 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <Input
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="e.g., Mathematics"
                      required
                      className="focus:outline-none focus:ring-0 border-0 bg-white transition-all h-10 py-2 text-base"
                      aria-required="true"
                    />
                    {!formData.subject && (
                      <p className="text-sm text-error mt-1">This field is required</p>
                    )}
                  </div>

                  <div className="form-control w-full">
                    <Label className="text-gray-700 flex items-center mb-1 text-sm">
                      <span className="font-medium">Exam Date</span>
                      <Tooltip text="Select the date when this exam will be administered">
                        <Info size={16} className="ml-1 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Input
                        type="date"
                        name="examDate"
                        value={formData.examDate}
                        onChange={handleInputChange}
                        className="focus:outline-none focus:ring-0 border-0 bg-white pr-10 h-10 py-2 text-base"
                      />
                      <Calendar size={18} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" />
                    </div>
                  </div>

                  <div className="form-control w-full">
                    <Label className="text-gray-700 flex items-center mb-1 text-sm">
                      <span className="font-medium">Duration</span>
                      <Tooltip text="Specify how long students have to complete this assessment">
                        <Info size={16} className="ml-1 text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Input
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        placeholder="e.g., 1 h 45 min"
                        className="focus:outline-none focus:ring-0 border-0 bg-white pr-10 h-10 py-2 text-base"
                      />
                      <Clock size={18} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-3 flex justify-end gap-3 mt-3">
              {isClient ? (
                <>


                  {questions && questions.length > 0 ? (
                    <PDFDownloadButton
                      questions={questions}
                      examHeader={examHeader}
                      printOptions={printOptions}
                      fileName={`${formData.subject.replace(/\s+/g, '-').toLowerCase()}-worksheet.pdf`}
                      isLoading={isLoading}
                      isFormValid={isFormValid}
                      onValidationError={() => {
                        setNotification({
                          message: 'Please fill in all required fields before downloading the PDF.',
                          type: 'error'
                        });
                      }}
                    />
                  ) : (
                    <div className="min-w-24 h-10 bg-gray-400 text-white rounded-md text-sm flex items-center justify-center gap-2 px-4 opacity-50 cursor-not-allowed">
                      <Download size={16} />
                      No questions to export
                    </div>
                  )}
                </>
              ) : null}
            </div>
          </div>
        </div>

        <form method="dialog" className="modal-backdrop">
          <button onClick={onClose} aria-label="Close modal background">close</button>
        </form>
      </dialog>
    </>
  );
};
