'use client';

import React from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';

export type AlertType = 'success' | 'error';

export interface AlertMessageProps {
  type: AlertType;
  message: string | null;
}

export const AlertMessage: React.FC<AlertMessageProps> = ({ type, message }) => {
  if (!message) return null;

  const isSuccess = type === 'success';
  
  return (
    <div className={`${isSuccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border p-2.5 mb-3 rounded-lg shadow-sm`}>
      <div className="flex items-center">
        <div className={`flex-shrink-0 ${isSuccess ? 'text-green-500' : 'text-red-500'}`}>
          {isSuccess ? <CheckCircle size={18} /> : <AlertCircle size={18} />}
        </div>
        <div className="ml-2">
          <h3 className={`text-xs font-medium ${isSuccess ? 'text-green-800' : 'text-red-800'}`}>
            {isSuccess ? 'Success' : 'Error'}
          </h3>
          <p className={`text-xs ${isSuccess ? 'text-green-700' : 'text-red-700'} mt-0.5`}>
            {message}
          </p>
        </div>
      </div>
    </div>
  );
};