'use client';

import React from 'react';
import { Input, InputProps } from '@/components/atoms/Input/Input';
import { cn } from '@/utils/cn';

export interface InputWithIconProps extends Omit<InputProps, 'hasLeftIcon' | 'hasRightIcon'> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconClick?: () => void;
  rightIconAriaLabel?: string;
}

export const InputWithIcon = React.forwardRef<HTMLInputElement, InputWithIconProps>(
  ({ 
    leftIcon, 
    rightIcon, 
    onRightIconClick, 
    rightIconAriaLabel,
    className,
    ...props 
  }, ref) => {
    return (
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            <div className="text-gray-500">
              {leftIcon}
            </div>
          </div>
        )}

        {/* Input */}
        <Input
          ref={ref}
          hasLeftIcon={!!leftIcon}
          hasRightIcon={!!rightIcon}
          className={className}
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
            {onRightIconClick ? (
              <button
                type="button"
                onClick={onRightIconClick}
                className="text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700 transition-colors"
                aria-label={rightIconAriaLabel}
              >
                {rightIcon}
              </button>
            ) : (
              <div className="text-gray-500 pointer-events-none">
                {rightIcon}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

InputWithIcon.displayName = 'InputWithIcon';
