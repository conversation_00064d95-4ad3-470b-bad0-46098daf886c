'use client';

import { handleGenerateWorksheet } from '@/actions/worksheet.action';
import { Button } from '@/components/atoms/Button/Button';
import Steps from '@/components/atoms/Steps/Steps';
import FormItem from '@/components/molecules/FormItems/FormItem';
import { TRHFQuestionValue } from '@/components/molecules/FormItems/RHFQuestion/RHFQuestion';
import { OptionTypes } from '@/components/molecules/FormItems/FormItem.type';
import { convertObjectToFormData } from '@/helpers/convertObjectToFormData';
import { DEFAULT_QUESTION_COUNT } from '@/utils/constants';
import { parseQuestionCount } from '@/utils/questionUtils';
import { useEffect, useState } from 'react';
import { useFormStatus } from 'react-dom';
import { FormProvider, useForm, useWatch } from 'react-hook-form';

// Define a more specific type for formSetting
export type TFormSetting = {
  name: string;
  type?: OptionTypes; // Use OptionTypes for better type safety
  question?: string;
  options?: { // options is optional for types like 'topicSelector'
    value: string;
    label: string;
    isActive?: boolean;
  }[];
  enableCustomInput?: boolean; // For RHFQuestion custom input
  // Potentially other fields if 'any' was hiding them
};

export type TConfigItem = { // Renamed TConfig to TConfigItem for clarity if TConfig is used elsewhere
  stepLabel: string;
  formSetting: TFormSetting | any; // Retain | any for flexibility if other types exist, but prefer TFormSetting
};

export type TConfig = TConfigItem[]; // TConfig is an array of TConfigItem

type TManageWorksheetViewProps = {
  config: TConfig;
};

export const ManageWorksheetView: React.FC<TManageWorksheetViewProps> = ({
  config,
}) => {
  const [currenStep, setCurrentStep] = useState(0);
  const { pending } = useFormStatus();
  const [currentActualTotalQuestions, setCurrentActualTotalQuestions] = useState(DEFAULT_QUESTION_COUNT); // Use constant

  const isLastStep = currenStep === config.length - 1;

  const methods = useForm({});
  
  // Define maxStep before onNextStep uses it
  const maxStep = config.length - 1;

  const onNextStep = () => {
    const newStep = currenStep + 1;
    if (newStep <= maxStep) { 
      setCurrentStep(newStep);
    }
  };

  const onPreStep = () => {
    const newStep = currenStep - 1;
    if (newStep < 0) return;
    setCurrentStep((preStep) => preStep - 1);
  };

  // Watch for the question_count value to pass to the question types step
  const questionCountValue = useWatch({
    control: methods.control,
    name: 'question_count',
    // defaultValue: { value: '20', label: '20 Questions', isCustom: false } // More appropriate default
  }) as TRHFQuestionValue | string | undefined;

  useEffect(() => {
    const newTotal = parseQuestionCount(questionCountValue, DEFAULT_QUESTION_COUNT);
    setCurrentActualTotalQuestions(newTotal);
  }, [questionCountValue]);

  const onSubmit = async (values: any) => {
    // Data transformation for question_count
    if (values.question_count !== undefined) {
      const originalQcValue = values.question_count as TRHFQuestionValue | string | undefined;
      values.question_count = parseQuestionCount(originalQcValue, DEFAULT_QUESTION_COUNT);

      // Determine isCustomQuestionCount based on the original structure
      if (typeof originalQcValue === 'object' && originalQcValue !== null && 'isCustom' in originalQcValue) {
        values.isCustomQuestionCount = !!originalQcValue.isCustom; // Ensure boolean
      } else {
        // If it's a direct string or number, or undefined, assume not custom.
        // The utility function handles undefined by returning default, which isn't "custom" in this context.
        values.isCustomQuestionCount = false;
      }
    } else {
      // If question_count was not in form values at all, set a default and mark not custom
      values.question_count = DEFAULT_QUESTION_COUNT;
      values.isCustomQuestionCount = false;
    }

    if (isLastStep) {
      // Filter question_type to remove items with count 0
      if (values.question_type && Array.isArray(values.question_type)) {
        values.question_type = values.question_type.filter(
          (qt: { count: number }) => qt.count !== 0
        );
      }

      // Create a new object for submission, explicitly excluding totalQuestions
      const { totalQuestions, ...submissionValues } = values;

      const formData: any = convertObjectToFormData(submissionValues);
      await handleGenerateWorksheet(formData);
    } else {
      onNextStep(); // Move to next step if not the last one
    }
  };
  // const maxStep = config.length - 1; // Moved this definition up

  if (pending) return <div>pending submit</div>;

  return (
    <div className="flex flex-col gap-6 w-full pt-8">
      <div className="text-black-100 font-semibold text-xl">
        Create Worksheet
      </div>
      <div>
        <Steps
          key={`steps-${config?.length}-${config?.map(c => c.stepLabel).join('-')}`}
          currentStep={currenStep}
          className="w-full"
          steps={config?.map((c) => ({ label: c.stepLabel }))}
        />
      </div>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormProvider {...methods}>
          {config?.map((c, index) => {
            const isHidden = currenStep !== config.indexOf(c);
            const formSetting = c.formSetting as TFormSetting; // Use the more specific type

            const isQuestionTypesStep = formSetting?.type === 'questionTypes';

            const attributes: any = { // Use any for attributes for now, can be typed better
              name: formSetting.name,
              options: isQuestionTypesStep
                ? {
                    // ...formSetting.options, // This was spreading array into object
                    type: {
                      questionTypes: formSetting.options, // This should be the options array
                      totalQuestions: currentActualTotalQuestions, // Use the state variable
                    }
                  }
                : formSetting.options,
              hidden: isHidden,
              question: formSetting?.question,
            };
            
            // Pass enableCustomInput if it exists in formSetting
            if (formSetting.enableCustomInput !== undefined) {
              attributes.enableCustomInput = formSetting.enableCustomInput;
            }

            const itemKey = isQuestionTypesStep
              ? `form-item-${index}-${c.stepLabel}-${formSetting.name}-${currentActualTotalQuestions}`
              : `form-item-${index}-${c.stepLabel}-${formSetting.name}`;

            return (
              <FormItem
                key={itemKey}
                type={formSetting?.type || 'question'}
                attributes={attributes}
              />
            );
          })}

          <div className="flex gap-6 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                onPreStep();
              }}
              disabled={currenStep === 0}
              type="button"
            >
              Prev
            </Button>
            <Button type="submit">{isLastStep ? 'Generate' : 'Next'}</Button>
          </div>
        </FormProvider>
      </form>
    </div>
  );
};
