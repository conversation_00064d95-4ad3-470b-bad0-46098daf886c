'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export interface DetailPageHeaderProps {
  title: string;
  backLink: string;
  backText?: string;
  className?: string;
}

export const DetailPageHeader: React.FC<DetailPageHeaderProps> = ({
  title,
  backLink,
  backText = 'Back',
  className = '',
}) => {
  return (
    <div className={`flex items-center mb-4 ${className}`}>
      <Link 
        href={backLink} 
        className="btn btn-ghost btn-sm mr-3"
      >
        <ArrowLeft size={16} />
        {backText}
      </Link>
      <h1 className="text-xl font-bold">{title}</h1>
    </div>
  );
};