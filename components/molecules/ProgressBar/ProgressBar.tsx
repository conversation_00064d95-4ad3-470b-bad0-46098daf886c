'use client';

import React from 'react';
import { cn } from '@/utils/cn';
import Icon from '@/components/atoms/Icon';

export type ProgressData = {
  current: number;
  total: number;
  percentage: number;
};

export interface ProgressBarProps {
  progress: ProgressData;
  className?: string;
  label?: string;
  showCurrentTotal?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  className,
  label = 'Generating worksheet...',
  showCurrentTotal = true
}) => {
  return (
    <div className={cn(
      "fixed bottom-0 right-0 z-20 bg-white border-t border-gray-200/60",
      className
    )}
    style={{ left: '281px', width: 'calc(100vw - 281px)' }}>
      <div className="w-full max-w-5xl mx-auto px-4 py-2">
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative flex items-center justify-center mr-1">
                {/* Simple AI icon with primary color */}
                <div className="relative w-6 h-6 flex items-center justify-center">
                  {/* Simple core */}
                  <div className="absolute w-2 h-2 bg-primary rounded-full animate-pulse"></div>

                  {/* Rotating rings */}
                  <div className="absolute w-full h-full">
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[85%] h-[85%] rounded-full border border-primary animate-spin duration-[6s]"></div>
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[65%] h-[65%] rounded-full border border-dashed border-primary animate-spin duration-[4s] direction-reverse"></div>
                  </div>

                  {/* Orbiting dots */}
                  <div className="absolute w-full h-full animate-spin duration-[3s]">
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1.5 h-1.5 bg-primary rounded-full"></div>
                  </div>
                  <div className="absolute w-full h-full animate-spin duration-[5s] direction-reverse">
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-1.5 h-1.5 bg-primary rounded-full"></div>
                  </div>
                  <div className="absolute w-full h-full animate-spin duration-[4s]">
                    <div className="absolute top-1/2 right-0 -translate-y-1/2 w-1.5 h-1.5 bg-primary rounded-full"></div>
                  </div>
                </div>
              </div>
              <div className="text-xs font-medium text-gray-700">
                {label}
              </div>
            </div>

            {/* Percentage indicator */}
            <div className="text-xs font-medium flex items-center gap-2">
              {showCurrentTotal && (
                <span className="bg-white px-2 py-0.5 rounded-full text-xs text-gray-800 border border-gray-200 shadow-sm">
                  {progress.current}/{progress.total}
                </span>
              )}
              <div className="relative">
                <span className="text-xs font-semibold text-gray-700">
                  {progress.percentage}%
                </span>
                <span className="absolute -bottom-1 left-0 w-full h-px bg-gray-200"></span>
              </div>
            </div>
          </div>

          {/* More visible AI generative progress bar */}
          <div className="relative w-full h-3 bg-gray-200 rounded-full overflow-hidden shadow-inner border border-gray-300/30">
            {/* Clear background pattern */}
            <div className="absolute inset-0 w-full h-full">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent"></div>
            </div>

            {/* Highly visible progress fill */}
            <div
              className="absolute top-0 left-0 h-full transition-all duration-500 overflow-hidden rounded-full"
              style={{ width: `${progress.percentage}%` }}
            >
              {/* Solid color base */}
              <div className="absolute inset-0 bg-primary"></div>

              {/* Clear wave pattern with high contrast */}
              <div className="absolute inset-0">
                {/* Subtle light effect */}
                <div className="absolute inset-0 bg-white/10 animate-pulse"></div>
              </div>
            </div>

            {/* Bright leading edge */}
            {progress.percentage > 0 && (
              <div
                className="absolute top-0 h-full w-1 bg-white shadow-md"
                style={{
                  left: `calc(${progress.percentage}% - 1px)`
                }}
              ></div>
            )}

            {/* No markers */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
