'use client';

import React from 'react';
import { AlertTriangle, Info, XCircle } from 'lucide-react';

export type RbacAlertType = 'error' | 'warning' | 'info';

export interface RbacAlertProps {
  type: RbacAlertType;
  message: string;
  className?: string;
}

/**
 * RBAC Alert Component for displaying user-friendly error messages
 * related to Role-Based Access Control restrictions, particularly for INDEPENDENT_TEACHER role.
 * 
 * Uses DaisyUI alert components for consistent styling:
 * - alert-error: For critical errors like 403 Forbidden access
 * - alert-warning: For non-critical issues or restrictions  
 * - alert-info: For providing guidance or contextual information
 */
export const RbacAlert: React.FC<RbacAlertProps> = ({ 
  type, 
  message, 
  className = '' 
}) => {
  const getAlertConfig = () => {
    switch (type) {
      case 'error':
        return {
          alertClass: 'alert-error',
          icon: <XCircle className="h-6 w-6 shrink-0" />,
          ariaLabel: 'Error alert'
        };
      case 'warning':
        return {
          alertClass: 'alert-warning', 
          icon: <AlertTriangle className="h-6 w-6 shrink-0" />,
          ariaLabel: 'Warning alert'
        };
      case 'info':
        return {
          alertClass: 'alert-info',
          icon: <Info className="h-6 w-6 shrink-0" />,
          ariaLabel: 'Information alert'
        };
      default:
        return {
          alertClass: 'alert-info',
          icon: <Info className="h-6 w-6 shrink-0" />,
          ariaLabel: 'Information alert'
        };
    }
  };

  const { alertClass, icon, ariaLabel } = getAlertConfig();

  return (
    <div 
      role="alert" 
      className={`alert ${alertClass} ${className}`}
      aria-label={ariaLabel}
    >
      {icon}
      <span>{message}</span>
    </div>
  );
};

// Pre-configured RBAC messages for common INDEPENDENT_TEACHER scenarios
export const RBAC_MESSAGES = {
  // Error messages (403 Forbidden scenarios)
  ACCESS_DENIED: "Access Denied: You do not have permission to access this resource.",
  USER_MANAGEMENT_FORBIDDEN: "Access Denied: Independent Teachers cannot manage user accounts.",
  SCHOOL_DELETE_FORBIDDEN: "Access Denied: Independent Teachers cannot delete schools.",
  OTHER_SCHOOL_ACCESS: "Access Denied: You can only access your own school's information.",
  
  // Warning messages (limitations/restrictions)
  FEATURE_LIMITED: "Feature Limited: To unlock all functionalities, please complete your school profile.",
  SCHOOL_LIMIT_REACHED: "School Limit: Independent Teachers can create and manage only one school.",
  PROFILE_INCOMPLETE: "Profile Incomplete: Some features may be limited until your school setup is complete.",
  
  // Info messages (guidance/contextual information)
  INDEPENDENT_TEACHER_GUIDANCE: "Guidance: Independent Teachers manage their school details here. To add students, first ensure your school is set up.",
  SCHOOL_SETUP_INFO: "Information: As an Independent Teacher, you can create your own school and manage its details.",
  NEXT_STEPS_INFO: "Next Steps: Complete your school profile to access all available features."
} as const;

export default RbacAlert;
