'use client';

import React from 'react';
import { XCircle } from 'lucide-react';

export interface ErrorAlertProps {
  message: string;
  className?: string;
}

/**
 * ErrorAlert Component for critical RBAC errors like 403 Forbidden access.
 * 
 * Uses DaisyUI's alert-error class for styling to convey high severity.
 * Intended for critical errors such as access denied scenarios for INDEPENDENT_TEACHER role.
 */
export const ErrorAlert: React.FC<ErrorAlertProps> = ({ 
  message, 
  className = '' 
}) => {
  return (
    <div 
      role="alert" 
      className={`alert alert-error ${className}`}
      aria-label="Error alert"
    >
      <XCircle className="h-6 w-6 shrink-0 stroke-current" />
      <span>{message}</span>
    </div>
  );
};

export default ErrorAlert;
