'use client';

import React from 'react';
import { Info } from 'lucide-react';

export interface InfoAlertProps {
  message: string;
  className?: string;
}

/**
 * InfoAlert Component for providing guidance or contextual information.
 * 
 * Uses DaisyUI's alert-info class for styling that is neutral and informative.
 * Intended for providing helpful guidance or contextual information to INDEPENDENT_TEACHER users.
 */
export const InfoAlert: React.FC<InfoAlertProps> = ({ 
  message, 
  className = '' 
}) => {
  return (
    <div 
      role="alert" 
      className={`alert alert-info ${className}`}
      aria-label="Information alert"
    >
      <Info className="h-6 w-6 shrink-0 stroke-current" />
      <span>{message}</span>
    </div>
  );
};

export default InfoAlert;
