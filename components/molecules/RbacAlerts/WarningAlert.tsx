'use client';

import React from 'react';
import { AlertTriangle } from 'lucide-react';

export interface WarningAlertProps {
  message: string;
  className?: string;
}

/**
 * WarningAlert Component for non-critical RBAC issues or limitations.
 * 
 * Uses DaisyUI's alert-warning class for styling to reflect a cautionary tone.
 * Intended for non-critical issues or potential limitations that INDEPENDENT_TEACHER users should be aware of.
 */
export const WarningAlert: React.FC<WarningAlertProps> = ({ 
  message, 
  className = '' 
}) => {
  return (
    <div 
      role="alert" 
      className={`alert alert-warning ${className}`}
      aria-label="Warning alert"
    >
      <AlertTriangle className="h-6 w-6 shrink-0 stroke-current" />
      <span>{message}</span>
    </div>
  );
};

export default WarningAlert;
