'use client';

import React from 'react';
import { LucideIcon } from '@/components/atoms/Icon/LucideIcon';

export interface ErrorDisplayProps {
  error: string | null;
  className?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  className = '',
}) => {
  if (!error) return null;

  return (
    <div className={`alert alert-error mb-4 ${className}`}>
      <LucideIcon variant="alert-triangle" size={18} />
      <span>Error: {error}</span>
    </div>
  );
};