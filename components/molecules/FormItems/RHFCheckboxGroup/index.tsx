'use client';

import { Controller, useFormContext } from 'react-hook-form';
import { cn } from '@/utils/cn';
import React, { useState, useCallback, useMemo } from 'react';

export type CheckboxOption = {
  label: string;
  value: string;
  description?: string;
  children?: CheckboxOption[];
};

export type RHFCheckboxGroupProps = {
  name: string;
  question: string;
  options: CheckboxOption[];
  className?: string;
  description?: string;
  tooltip?: string;
  layout?: 'vertical' | 'horizontal' | 'grid';
  columns?: number;
  autoSelectChildren?: boolean;
  autoSelectParent?: boolean;
  disableChildren?: boolean;
  indentSize?: number;
  showSelectAll?: boolean;
  selectAllLabel?: string;
  structuredData?: boolean; // Flag to use new structured data format
  defaultExpanded?: boolean; // Whether child checkboxes are expanded by default
  expandIcon?: React.ReactNode; // Custom expand icon
  collapseIcon?: React.ReactNode; // Custom collapse icon
};

// Helper function to get all descendant values of an option
const getAllDescendantValues = (option: CheckboxOption): string[] => {
  const values: string[] = [option.value];

  if (option.children && option.children.length > 0) {
    option.children.forEach(child => {
      values.push(...getAllDescendantValues(child));
    });
  }

  return values;
};

export const RHFCheckboxGroup: React.FC<RHFCheckboxGroupProps> = ({
                                                                    name,
                                                                    question,
                                                                    options,
                                                                    className,
                                                                    layout = 'vertical',
                                                                    columns = 3,
                                                                    autoSelectChildren = true,
                                                                    autoSelectParent = true,
                                                                    disableChildren = true,
                                                                    indentSize = 6,
                                                                    showSelectAll = false,
                                                                    selectAllLabel = 'Select All',
                                                                    structuredData = false,
                                                                    defaultExpanded = false,
                                                                    expandIcon,
                                                                    collapseIcon,
                                                                  }) => {
  const { control, watch, setValue, formState: { errors } } = useFormContext();

  // State to track expanded/collapsed state for each parent checkbox
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // Watch the current values
  const watchedValues = watch(name);

  // Handle both structured and flat data formats
  const currentValues = React.useMemo(() => {
    if (!watchedValues) return [];

    // If using structured data format
    if (structuredData && Array.isArray(watchedValues)) {
      // Extract all item IDs from the structured data
      const allItemIds: string[] = [];
      watchedValues.forEach(item => {
        // Add the main ID
        allItemIds.push(item.id);

        // Add all items if they exist
        if (item.items && Array.isArray(item.items)) {
          // Check if items are objects with id property or just strings
          if (item.items.length > 0 && typeof item.items[0] === 'object' && item.items[0] !== null) {
            allItemIds.push(...item.items.map((subItem: any) => subItem.id));
          } else {
            allItemIds.push(...item.items);
          }
        }
      });
      return allItemIds;
    }

    // If using flat array format (backward compatibility)
    return watchedValues || [];
  }, [watchedValues, structuredData]);

  // Function to determine if all children of an option are selected
  const areAllChildrenSelected = useCallback((option: CheckboxOption): boolean => {
    if (!option.children || option.children.length === 0) {
      return true;
    }

    const childValues = getAllDescendantValues(option).filter(val => val !== option.value);
    return childValues.every(value => currentValues.includes(value));
  }, [currentValues]);

  // Function to determine if some (but not all) children of an option are selected
  const areSomeChildrenSelected = useCallback((option: CheckboxOption): boolean => {
    if (!option.children || option.children.length === 0) {
      return false;
    }

    const childValues = getAllDescendantValues(option).filter(val => val !== option.value);
    const hasSelected = childValues.some(value => currentValues.includes(value));
    const allSelected = childValues.every(value => currentValues.includes(value));

    return hasSelected && !allSelected;
  }, [currentValues]);

  // Effect to auto-select parents when all children are selected
  React.useEffect(() => {
    if (!autoSelectParent) return;

    let needsUpdate = false;
    const newValues = [...currentValues];

    // Check each parent option
    options.forEach(option => {
      if (!option.children || option.children.length === 0) return;

      const childValues = getAllDescendantValues(option).filter(val => val !== option.value);
      const allChildrenSelected = childValues.every(value => currentValues.includes(value));
      const parentSelected = currentValues.includes(option.value);

      // If all children are selected but parent is not, add parent
      if (allChildrenSelected && !parentSelected) {
        needsUpdate = true;
        newValues.push(option.value);
      }
      // If not all children are selected but parent is, remove parent
      else if (!allChildrenSelected && parentSelected && !areSomeChildrenSelected(option)) {
        needsUpdate = true;
        const index = newValues.indexOf(option.value);
        if (index !== -1) {
          newValues.splice(index, 1);
        }
      }
    });

    // Update form values if needed
    if (needsUpdate) {
      if (structuredData) {
        // Convert to structured format
        const structuredValues = options.map(option => {
          const isSelected = newValues.includes(option.value);
          if (!isSelected) return null;

          // Find child values that are selected
          const childItems = option.children
            ? option.children
                .filter(child => newValues.includes(child.value))
                .map(child => ({
                  id: child.value,
                  label: child.label
                }))
            : [];

          return {
            id: option.value,
            label: option.label,
            items: childItems
          };
        }).filter(Boolean);

        setValue(name, structuredValues, { shouldDirty: true });
      } else {
        // Use flat array format
        setValue(name, newValues, { shouldDirty: true });
      }
    }
  }, [currentValues, options, autoSelectParent, name, setValue, structuredData, areSomeChildrenSelected]);

  // Toggle expanded state for a parent checkbox
  const toggleExpanded = useCallback((optionValue: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [optionValue]: !prev[optionValue]
    }));
  }, []);

  // Function to handle select all
  const handleSelectAll = (checked: boolean) => {
    const allValues = checked
      ? options.flatMap(option => getAllDescendantValues(option))
      : [];

    if (structuredData) {
      // Group values by their parent-child relationships
      const structuredValues = options.map(option => {
        const optionValue = option.value;
        const childValues = option.children
          ? option.children.flatMap(child => getAllDescendantValues(child))
          : [];

        // Find the option to get its label
        const optionObj = options.find(opt => opt.value === optionValue);

        // Format child items as objects with id and label
        const childItems = childValues.map(childValue => {

          // Find this child in the options to get its label
          let childLabel = childValue;
          for (const parentOpt of options) {
            if (parentOpt.children) {
              const foundChild = parentOpt.children.find(child => child.value === childValue);
              if (foundChild) {
                childLabel = foundChild.label;
                break;
              }
            }
          }

          return {
            id: childValue,
            label: childLabel
          };
        });

        return {
          id: optionValue,
          label: optionObj?.label || '',
          items: checked ? childItems : []
        };
      });

      setValue(name, structuredValues, {
        shouldDirty: true
      });
    } else {
      // Use flat array format for backward compatibility
      setValue(name, allValues, {
        shouldDirty: true
      });
    }
  };

  // Check if all options are selected
  const allSelected = options.length > 0 &&
    options.flatMap(option => getAllDescendantValues(option))
      .every(value => currentValues.includes(value));

  // Initialize expanded state based on defaultExpanded prop
  React.useEffect(() => {
    if (defaultExpanded) {
      const initialExpandedState: Record<string, boolean> = {};
      options.forEach(option => {
        if (option.children && option.children.length > 0) {
          initialExpandedState[option.value] = true;
        }
      });
      setExpandedItems(initialExpandedState);
    }
  }, [defaultExpanded, options]);

  // Create a map of checkbox refs to handle indeterminate state
  const checkboxRefs = React.useRef<Record<string, HTMLInputElement | null>>({});

  // Effect to update indeterminate state for all checkboxes
  React.useEffect(() => {
    options.forEach(option => {
      const hasChildren = option.children && option.children.length > 0;
      if (hasChildren) {
        const someChildrenSelected = areSomeChildrenSelected(option);
        const allChildrenSelected = areAllChildrenSelected(option);
        const isSelected = currentValues.includes(option.value);
        const effectivelySelected = isSelected || (autoSelectParent && allChildrenSelected);

        const checkboxRef = checkboxRefs.current[option.value];
        if (checkboxRef) {
          checkboxRef.indeterminate = someChildrenSelected && !effectivelySelected;
        }
      }
    });
  }, [currentValues, options, autoSelectParent, areSomeChildrenSelected, areAllChildrenSelected]);

  const renderCheckboxes = (
    options: CheckboxOption[],
    level = 0,
    parentSelected = false
  ) => {
    return options.map((option) => (
      <div key={option.value} className={cn(
        "pl-" + (level * indentSize),
        level > 0 && "ml-1 border-l-2 border-gray-100"
      )}>
        <Controller
          control={control}
          name={name}
          render={({ field }) => {
            const values = field.value || [];

            // Determine if this option is selected based on the data format
            let isSelected = false;
            if (structuredData && Array.isArray(values)) {
              // For structured data, check if this option's ID is in the array of objects
              isSelected = values.some(item => item.id === option.value);
            } else {
              // For flat array format, check if the value is in the array
              isSelected = values.includes(option.value);
            }

            const hasChildren = option.children && option.children.length > 0;

            // Check if all children are selected to auto-select parent
            const allChildrenSelected = areAllChildrenSelected(option);

            // Check if some children are selected for indeterminate state
            const someChildrenSelected = areSomeChildrenSelected(option);

            // If parent is selected or all children are selected (if autoSelectParent is true)
            const effectivelySelected =
              parentSelected ||
              isSelected ||
              (autoSelectParent && hasChildren && allChildrenSelected);

            // Check if this item is expanded
            const isExpanded = !!expandedItems[option.value];

            return (
              <div className={cn(
                "mb-2 p-2 rounded-md transition-all duration-300 ease-in-out",
                "border border-gray-200",
                effectivelySelected && "bg-primary-50 border-primary-100",
                someChildrenSelected && !effectivelySelected && "bg-gray-50 border-gray-300"
              )}>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <input
                      id={`checkbox-${option.value}`}
                      ref={(el) => {
                        checkboxRefs.current[option.value] = el;
                      }}
                      type="checkbox"
                      className={cn(
                        'checkbox',
                        'text-white rounded-md',
                        'checked:border-primary checked:bg-primary checked:text-white',
                        'focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50',
                        'transition-all duration-300 ease-in-out',
                        hasChildren && 'checkbox-md',
                        !hasChildren && 'checkbox-sm'
                      )}
                      checked={effectivelySelected}
                      disabled={disableChildren && parentSelected} // Disable if parent is selected and disableChildren is true
                      onChange={(e) => {
                        if (structuredData) {
                          // Get current structured values or initialize empty array
                          const currentStructuredValues = Array.isArray(field.value) ? [...field.value] : [];

                          // Find if this option's ID is already in the structured data
                          const optionIndex = currentStructuredValues.findIndex(item => item.id === option.value);

                          if (e.target.checked) {
                            // If checking the box

                            // Get all child values if autoSelectChildren is true and the option has children
                            let childValues: Array<{id: string, label: string}> = [];
                            if (autoSelectChildren && option.children && option.children.length > 0) {
                              // Get all descendant values except the current option
                              const descendantValues = getAllDescendantValues(option).filter(val => val !== option.value);

                              // Convert to array of objects with id and label
                              childValues = descendantValues.map(childValue => {
                                // Find this child in the options to get its label
                                let childLabel = childValue;

                                // Find the child option to get its label
                                for (const child of option.children || []) {
                                  if (child.value === childValue) {
                                    childLabel = child.label;
                                    break;
                                  }

                                  // Check for grandchildren
                                  if (child.children) {
                                    const grandchild = child.children.find(gc => gc.value === childValue);
                                    if (grandchild) {
                                      childLabel = grandchild.label;
                                      break;
                                    }
                                  }
                                }

                                return {
                                  id: childValue,
                                  label: childLabel
                                };
                              });
                            }

                            if (optionIndex >= 0) {
                              // Update existing entry
                              currentStructuredValues[optionIndex] = {
                                ...currentStructuredValues[optionIndex],
                                label: option.label,
                                items: childValues
                              };
                            } else {
                              // Add new entry
                              currentStructuredValues.push({
                                id: option.value,
                                label: option.label,
                                items: childValues
                              });
                            }
                          } else {
                            // If unchecking the box
                            if (optionIndex >= 0) {
                              // Remove this entry
                              currentStructuredValues.splice(optionIndex, 1);
                            }

                            // Also remove any entries where this option is in the items array
                            for (let i = currentStructuredValues.length - 1; i >= 0; i--) {
                              const entry = currentStructuredValues[i];
                              if (entry.items && Array.isArray(entry.items)) {
                                // Filter items, handling both object and string formats
                                if (entry.items.length > 0 && typeof entry.items[0] === 'object') {
                                  entry.items = entry.items.filter(
                                    (item: any) => item.id !== option.value
                                  );
                                } else {
                                  entry.items = entry.items.filter(
                                    (id: string) => id !== option.value
                                  );
                                }
                              }
                            }
                          }

                          // For structured data, we need to handle parent-child relationships
                          // If this is a child checkbox being checked, we need to ensure the parent is included
                          if (e.target.checked && level > 0) {
                            // Find the parent option
                            for (const parentOption of options) {
                              if (parentOption.children && parentOption.children.some(child => child.value === option.value)) {
                                // Check if all siblings are now checked
                                const allSiblingsChecked = parentOption.children.every(child => {
                                  // Check if this child is in the currentStructuredValues
                                  return currentStructuredValues.some(item => {
                                    if (item.id === parentOption.value && item.items) {
                                      return item.items.some((subItem: any) =>
                                        (typeof subItem === 'object' ? subItem.id === child.value : subItem === child.value)
                                      );
                                    }
                                    return item.id === child.value;
                                  });
                                });

                                // If all siblings are checked, make sure parent is included
                                if (allSiblingsChecked) {
                                  const parentIndex = currentStructuredValues.findIndex(item => item.id === parentOption.value);
                                  if (parentIndex === -1) {
                                    // Add parent with all children
                                    const childItems = parentOption.children.map(child => ({
                                      id: child.value,
                                      label: child.label
                                    }));

                                    currentStructuredValues.push({
                                      id: parentOption.value,
                                      label: parentOption.label,
                                      items: childItems
                                    });
                                  }
                                }
                                break;
                              }
                            }
                          }

                          field.onChange(currentStructuredValues);
                        } else {
                          // Original flat array implementation
                          const newValues = [...values];

                          if (e.target.checked) {
                            // Add this value if not already included
                            if (!newValues.includes(option.value)) {
                              newValues.push(option.value);
                            }

                            // Add all child values when a parent is checked (if autoSelectChildren is true)
                            if (autoSelectChildren && option.children && option.children.length > 0) {
                              const childValues = getAllDescendantValues(option).filter(val => val !== option.value);
                              childValues.forEach(childValue => {
                                if (!newValues.includes(childValue)) {
                                  newValues.push(childValue);
                                }
                              });
                            }

                            // If this is a child checkbox, check if all siblings are now checked
                            // If so, we should also check the parent (handled by the useEffect)
                          } else {
                            // Remove this value
                            const index = newValues.indexOf(option.value);
                            if (index !== -1) {
                              newValues.splice(index, 1);
                            }

                            // Remove all child values when a parent is unchecked (if autoSelectChildren is true)
                            if (autoSelectChildren && option.children && option.children.length > 0) {
                              const childValues = getAllDescendantValues(option).filter(val => val !== option.value);
                              childValues.forEach(childValue => {
                                const childIndex = newValues.indexOf(childValue);
                                if (childIndex !== -1) {
                                  newValues.splice(childIndex, 1);
                                }
                              });
                            }

                            // If this is a child and its parent is checked, uncheck the parent
                            if (level > 0 && parentSelected) {
                              // Find the parent option in the options array
                              for (const parentOption of options) {
                                if (parentOption.children && parentOption.children.some(child => child.value === option.value)) {
                                  const parentIndex = newValues.indexOf(parentOption.value);
                                  if (parentIndex !== -1) {
                                    newValues.splice(parentIndex, 1);
                                  }
                                  break;
                                }
                              }
                            }
                          }

                          field.onChange(newValues);
                        }
                      }}
                      aria-label={option.label}
                    />
                    {hasChildren && (
                      <span className={cn(
                        "absolute -right-1 -top-1 w-3 h-3 rounded-full",
                        effectivelySelected ? "bg-primary" : "bg-gray-300"
                      )}></span>
                    )}
                  </div>
                  <div className="flex-1">
                    <label
                      htmlFor={`checkbox-${option.value}`}
                      className="cursor-pointer w-full"
                    >
                      <span className={cn(
                        "text-base font-medium",
                        hasChildren && "text-primary-700",
                        effectivelySelected && "text-primary-800"
                      )}>
                        {option.label}
                      </span>
                      {option.description && (
                        <p className="text-sm text-gray-500 mt-1">{option.description}</p>
                      )}
                    </label>
                  </div>

                  {/* Expand/Collapse button for parent checkboxes */}
                  {hasChildren && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        toggleExpanded(option.value);
                      }}
                      className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                      aria-label={isExpanded ? 'Collapse' : 'Expand'}
                    >
                      {isExpanded ? (
                        collapseIcon || (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        )
                      ) : (
                        expandIcon || (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        )
                      )}
                    </button>
                  )}
                </div>

                {/* Child checkboxes with accordion behavior */}
                {hasChildren && (
                  <div className={cn(
                    "pl-1 overflow-hidden transition-all duration-300",
                    isExpanded ? "mt-2 max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
                  )}>
                    {renderCheckboxes(option.children as CheckboxOption[], level + 1, effectivelySelected)}
                  </div>
                )}
              </div>
            );
          }}
        />
      </div>
    ));
  };

  // Get error message if any
  const errorMessage = errors[name]?.message as string | undefined;

  // Determine layout class based on layout prop
  const getLayoutClass = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-row flex-wrap gap-3';
      case 'grid':
        return `grid grid-cols-1 md:grid-cols-${columns} gap-3`;
      case 'vertical':
      default:
        return 'flex flex-col gap-1';
    }
  };

  return (
    <div className={cn(
      "flex flex-col gap-4 bg-white shadow-md border rounded-lg border-gray-200 p-4",
      "transition-all duration-300 ease-in-out",
      "bg-gradient-to-br from-white to-gray-50",
      className
    )}>
      <div className="flex items-center gap-2  pb-3 ">
        <div className="text-xl font-semibold select-none w-fit text-primary-700 flex items-center">
          <span className="inline-block w-2 h-6 bg-primary-500 rounded-full mr-2"></span>
          {question}
        </div>
      </div>



      {showSelectAll && (
        <div className="mb-3 bg-gradient-to-r from-primary-50 to-white p-3 rounded-md border-l-4 border-primary shadow-sm transition-all duration-300 hover:shadow">
          <div className="flex items-center gap-2">
            <div className="relative">
              <input
                id="checkbox-select-all"
                type="checkbox"
                className={cn(
                  'checkbox checkbox-lg',
                  'text-white rounded-md',
                  'checked:bg-primary checked:text-white',
                  'focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50',
                  'transition-all duration-300 ease-in-out'
                )}
                checked={allSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
                aria-label={selectAllLabel}
              />
            </div>
            <div className="flex-1">
              <label htmlFor="checkbox-select-all" className="cursor-pointer w-full">
                <span className="text-base font-medium text-primary-700">{selectAllLabel}</span>
                <p className="text-xs text-gray-500 mt-1">Select or deselect all options at once</p>
              </label>
            </div>
          </div>
        </div>
      )}

      <div
        className={cn(
          getLayoutClass(),
          "rounded-md p-1 max-h-[500px] overflow-y-auto",
          "transition-all duration-300 ease-in-out",
          "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"
        )}
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(219, 234, 254, 0.8) rgba(243, 244, 246, 0.5)'
        }}
      >
        {renderCheckboxes(options)}
      </div>

      {errorMessage && (
        <div className="text-sm text-red-500 mt-2 p-3 bg-red-50 rounded-md border-l-4 border-red-500 flex items-center shadow-sm transition-all duration-300 hover:shadow">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 flex-shrink-0 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <p className="font-medium">{errorMessage}</p>
            <p className="text-xs text-red-400 mt-1">Please correct this error to continue</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default RHFCheckboxGroup;