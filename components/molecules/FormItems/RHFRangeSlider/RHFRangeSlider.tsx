import { Controller, useFormContext } from 'react-hook-form';
import React from 'react';
import { cn } from '@/utils/cn';
import { FormLabel } from '@/components/atoms/Form/Form';
import Slider from '@/components/atoms/Slider';

// Extend CSSProperties to include custom CSS properties
declare module 'react' {
  interface CSSProperties {
    '--thumb-color'?: string;
    '--thumb-border'?: string;
    '--thumb-size'?: string;
  }
}

export interface RHFRangeSliderProps {
  name: string;
  question: string;
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: number;
  showValue?: boolean;
  valuePrefix?: string;
  valueSuffix?: string;
  className?: string;
  description?: string;
}

export const RHFRangeSlider: React.FC<RHFRangeSliderProps> = ({
  name,
  question,
  min = 0,
  max = 100,
  step = 1,
  defaultValue,
  showValue = true,
  valuePrefix = '',
  valueSuffix = '',
  className,
  description,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  // Get error message if any
  const errorMessage = errors[name]?.message as string | undefined;

  return (
    <div
      className={cn(
        "flex flex-col gap-4 bg-white shadow-md border rounded-lg border-gray-200 p-4",
        "transition-all duration-300 ease-in-out",
        "bg-gradient-to-br from-white to-gray-50",
        className
      )}
    >
      <div className="flex items-center gap-2 pb-3">
        <div className="text-xl font-semibold select-none w-fit text-primary-700 flex items-center">
          <span className="inline-block w-2 h-6 bg-primary-500 rounded-full mr-2"></span>
          {question}
        </div>
      </div>

      {description && (
        <p className="text-sm text-gray-500 -mt-2 mb-2">{description}</p>
      )}

      <div className="p-6 bg-white rounded-md border border-gray-200 shadow-sm transition-all duration-300 hover:shadow">
        <Controller
          control={control}
          name={name}
          defaultValue={defaultValue}
          render={({ field }) => (
            <div className="space-y-6">
              {showValue && (
                <div className="text-center">
                  <span className="text-lg font-medium text-primary-700 bg-primary-50 px-4 py-2 rounded-full inline-block shadow-sm">
                    {valuePrefix}{field.value || defaultValue || min}{valueSuffix}
                  </span>
                </div>
              )}

              <Slider
                min={min}
                max={max}
                step={step}
                value={field.value}
                onChange={(value) => field.onChange(value)}
                showValue={false} /* We're showing the value in a better way above */
                valuePrefix={valuePrefix}
                valueSuffix={valueSuffix}
                className="py-6"
                trackClassName="bg-gray-200"
                progressClassName="bg-primary-500"
                thumbClassName="focus:ring-primary-500"
                style={{
                  '--thumb-color': 'white',
                  '--thumb-border': '2px solid #3872FA',
                  '--thumb-size': '28px', /* Slightly larger thumb in the form context */
                }}
              />

              <div className="flex justify-between text-sm font-medium">
                <span className="text-gray-600 bg-gray-100 px-3 py-1 rounded-md">{valuePrefix}{min}{valueSuffix}</span>
                <span className="text-gray-600 bg-gray-100 px-3 py-1 rounded-md">{valuePrefix}{max}{valueSuffix}</span>
              </div>
            </div>
          )}
        />
      </div>

      {errorMessage && (
        <div className="text-sm text-red-500 mt-2 p-3 bg-red-50 rounded-md border-l-4 border-red-500 flex items-center shadow-sm transition-all duration-300 hover:shadow">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 flex-shrink-0 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <p className="font-medium">{errorMessage}</p>
            <p className="text-xs text-red-400 mt-1">Please correct this error to continue</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default RHFRangeSlider;