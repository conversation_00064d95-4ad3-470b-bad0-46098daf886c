import {Input} from '@/components/atoms/Input/Input';
import React from 'react';
import {OptionTypes, TRHFProps} from './FormItem.type';
import RHFQuestion from './RHFQuestion';
import {TRHFQuestionProps} from './RHFQuestion/RHFQuestion';
import RHFQuestionWithNote from './RHFQuestionWithNote';
import TopicSelector from './TopicSelector';
import RHFRangeSlider from './RHFRangeSlider';

const FormItem: React.FC<TRHFProps<OptionTypes>> = ({ type, attributes }) => {

  if (attributes?.hidden) return null;
  if (type === 'input') return <Input {...attributes} />;
  if (type === 'question') {
    return <RHFQuestion {...(attributes as TRHFQuestionProps)} />;
  }
  if (type === 'questionTypes') {
    return <RHFQuestionWithNote {...attributes} />;
  }
  if (type === 'topicSelector') {
    return <TopicSelector {...attributes} />;
  }
  if (type === 'rangeSlider') {
    return <RHFRangeSlider {...attributes} />;
  }
};

export default FormItem;
