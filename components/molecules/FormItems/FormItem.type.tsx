import { InputProps } from '@/components/atoms/Input/Input';
import {TRHFQuestionProps} from './RHFQuestion/RHFQuestion';
import { TopicSelectorProps } from './TopicSelector';
import { RHFRangeSliderProps } from './RHFRangeSlider';

export type OptionTypes = 'input' | 'question' | 'questionTypes' | 'topicSelector' | 'rangeSlider';

export type TCommonProps={
  hidden?: boolean;
}

export type TMappingItems = {
  input: InputProps;
  question: TRHFQuestionProps;
  questionTypes: any;
  topicSelector: TopicSelectorProps;
  rangeSlider: RHFRangeSliderProps;
};

export type TRHFProps<T extends OptionTypes> = {
  type: T;
  attributes: TMappingItems[T] & TCommonProps;
};
