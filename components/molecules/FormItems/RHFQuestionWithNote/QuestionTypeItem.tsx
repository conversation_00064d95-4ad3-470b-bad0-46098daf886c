import React from 'react';
import { Input } from '@/components/atoms/Input/Input';
import { cn } from '@/utils/cn';

interface QuestionTypeItemProps {
  label: string;
  fieldName: string;
  count: number;
  percentage: number;
  totalQuestions: number;
  value: string;
  onChange: (fieldName: string, value: string) => void;
  onSliderChange: (fieldName: string, value: number) => void;
}

export const QuestionTypeItem: React.FC<QuestionTypeItemProps> = ({
  label,
  fieldName,
  count,
  percentage,
  totalQuestions,
  value,
  onChange,
  onSliderChange,
}) => {
  return (
    <div className="flex flex-col w-full gap-4">
      <div className="flex justify-between items-center w-full">
        <span className="font-inter text-base leading-normal text-black">{label}</span>
        <div className="flex items-center gap-4">
          <div className="flex justify-center items-center border border-base-300 rounded-lg w-16 px-2 py-1">
            <Input
              value={value || '0'}
              name={fieldName}
              min={0}
              max={totalQuestions}
              type="number"
              className="w-full font-inter text-base font-medium text-neutral text-center outline-none border-none p-0 h-6"
              onChange={(e) => {
                onChange(fieldName, e.target.value);
              }}
            />
          </div>
          <span className="font-inter text-base text-neutral text-right w-12">
            {percentage}%
          </span>
        </div>
      </div>

      {/* Slider with Pin */}
      <div className="relative py-2 w-full">
        {/* Background track */}
        <div className="absolute top-1/2 left-0 w-full h-2 -translate-y-1/2 bg-base-300 rounded-full"></div>

        {/* Progress fill */}
        <div
          className="absolute top-1/2 left-0 h-2 -translate-y-1/2 bg-primary rounded-l-full"
          style={{ width: `${percentage}%` }}
        ></div>

        {/* Range input */}
        <input
          type="range"
          min={0}
          max={totalQuestions}
          value={count}
          onChange={(e) => {
            const newValue = parseInt(e.target.value, 10);
            onSliderChange(fieldName, newValue);
          }}
          className={cn(
            "range w-full h-8 appearance-none bg-transparent cursor-pointer relative z-10",
            "focus:outline-none"
          )}
        />

        {/* Handle */}
        <div
          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-md z-20 pointer-events-none"
          style={{ left: `calc(${percentage}% - ${percentage > 0 ? '8px' : '0px'})` }}
        ></div>
      </div>
    </div>
  );
};
