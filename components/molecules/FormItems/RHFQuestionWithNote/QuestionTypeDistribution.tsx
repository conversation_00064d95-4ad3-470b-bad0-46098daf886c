import React from 'react';
import { QuestionTypeItem } from './QuestionTypeItem';

type QuestionTypeOption = {
  value: string;
  label: string;
  note: number;
  count?: number;
  percentage?: number;
};

interface QuestionTypeDistributionProps {
  questionTypeOptions: QuestionTypeOption[];
  totalQuestions: number;
  questionValues: Record<string, string>;
  percentages: Record<string, number>;
  counts: Record<string, number>;
  totalCount: number;
  question: string;
  onInputChange: (fieldName: string, value: string) => void;
  onSliderChange: (fieldName: string, value: number) => void;
}

export const QuestionTypeDistribution: React.FC<QuestionTypeDistributionProps> = ({
  questionTypeOptions,
  totalQuestions,
  questionValues,
  percentages,
  counts,
  totalCount,
  question,
  onInputChange,
  onSliderChange,
}) => {
  return (
    <div className="bg-white rounded-lg p-6 flex flex-col gap-6 w-full border border-base-300">
      {/* Header */}
      <div className="flex flex-col gap-2 w-full">
        <h2 className="font-inter text-xl font-semibold leading-normal text-black">
          {question}
        </h2>
        <p className="font-inter text-base leading-normal text-neutral">
          Distribute the types of questions you want to include. Total must add up to 100%.
        </p>
      </div>

      {/* Question Type Grid */}
      <div className="grid grid-cols-2 gap-6 w-full">
        {questionTypeOptions.map(({ label, value }) => {
          const fieldName = value;
          const percentage = percentages[fieldName] || 0;
          const count = counts[fieldName] || 0;

          return (
            <QuestionTypeItem
              key={fieldName}
              label={label}
              fieldName={fieldName}
              count={count}
              percentage={percentage}
              totalQuestions={totalQuestions}
              value={questionValues[fieldName] || '0'}
              onChange={onInputChange}
              onSliderChange={onSliderChange}
            />
          );
        })}
      </div>

      {/* Footer */}
      <p className={`font-inter text-base leading-normal ${totalCount === totalQuestions ? 'text-neutral' : 'text-error font-medium'}`}>
        Total: {totalCount} Questions ({Object.values(percentages).reduce((sum, p) => sum + p, 0)}%)
        {totalCount !== totalQuestions && ` (Should be ${totalQuestions})`}
      </p>
    </div>
  );
};
