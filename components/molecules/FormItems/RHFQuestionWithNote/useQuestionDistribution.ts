import { INITIAL_NON_ZERO_COUNT } from '@/utils/constants';
import { useState, useEffect, useCallback, useRef } from 'react'; // Added useCallback and useRef
import { UseFormSetValue, UseFormWatch, UseFormGetValues } from 'react-hook-form';

type QuestionTypeOption = {
  value: string;
  label: string;
  note: number;
  count?: number;
  percentage?: number;
};

interface UseQuestionDistributionProps {
  questionTypeOptions: QuestionTypeOption[];
  totalQuestions: number;
  setValue: UseFormSetValue<any>;
  getValues: UseFormGetValues<any>;
  watch: UseFormWatch<any>;
  name: string;
}

interface QuestionDistribution {
  questionValues: Record<string, string>;
  percentages: Record<string, number>;
  counts: Record<string, number>;
  totalCount: number;
  handleInputChange: (fieldName: string, value: string) => void;
  handleSliderChange: (fieldName: string, value: number) => void;
}

export const useQuestionDistribution = ({
  questionTypeOptions,
  totalQuestions,
  setValue,
  getValues,
  watch,
  name,
}: UseQuestionDistributionProps): QuestionDistribution => {
  const isDistributingRef = useRef(false);
  // Create state objects to store the values for each question type
  const [questionValues, setQuestionValues] = useState<Record<string, string>>({});
  const [percentages, setPercentages] = useState<Record<string, number>>({});
  const [counts, setCounts] = useState<Record<string, number>>({});

  const areObjectsNumericallyEqual = (objA: Record<string, number>, objB: Record<string, number>): boolean => {
    const keysA = Object.keys(objA);
    const keysB = Object.keys(objB);
    if (keysA.length !== keysB.length) return false;
    for (const key of keysA) {
      if (objA[key] !== objB[key] || !Object.prototype.hasOwnProperty.call(objB, key)) {
        return false;
      }
    }
    return true;
  };

  const setCountsIfChanged = useCallback((newCountsCandidate: Record<string, number>) => {
    setCounts(prevActualCounts => {
      if (areObjectsNumericallyEqual(prevActualCounts, newCountsCandidate)) {
        return prevActualCounts;
      }
      return newCountsCandidate;
    });
  }, []); // Empty dependency array as areObjectsNumericallyEqual is pure and setCounts is stable

  // Helper function to update percentages (memoized)
  const updatePercentages = useCallback((newCounts: Record<string, number>) => {
    const updatedTotalCount = Object.values(newCounts).reduce((sum, count) => sum + count, 0);
    const newPercentagesValue: Record<string, number> = {};

    questionTypeOptions.forEach(({ value }) => {
      newPercentagesValue[value] = updatedTotalCount > 0
        ? Math.round((newCounts[value] / updatedTotalCount) * 100)
        : 0;
    });

    setPercentages(newPercentagesValue);
  }, [questionTypeOptions]);

  // Helper function to redistribute excess when over the total (memoized)
  const redistributeExcess = useCallback((
    currentCounts: Record<string, number>,
    primaryField: string,
    excess: number
  ) => {
    // Find fields with non-zero values that can be reduced
    const fieldsToReduce = questionTypeOptions
      .filter(option => option.value !== primaryField && currentCounts[option.value] > 0)
      .map(option => option.value);

    const adjustedCounts = { ...currentCounts };

    if (fieldsToReduce.length > 0) {
      // Distribute the excess count reduction among other fields
      const baseReduction = Math.floor(excess / fieldsToReduce.length);
      let remainingReduction = excess % fieldsToReduce.length; // Use let for mutable remainder

      fieldsToReduce.forEach((field) => { // Removed index as it's not used for logic here
        const currentFieldCount = adjustedCounts[field];
        const extraReduction = remainingReduction > 0 ? 1 : 0;
        if (extraReduction > 0) remainingReduction--;

        const totalReduction = baseReduction + extraReduction;

        // Ensure we don't reduce below zero
        const newFieldCount = Math.max(0, currentFieldCount - totalReduction);
        adjustedCounts[field] = newFieldCount;

        // Update the form value
        setValue(field, newFieldCount.toString(), { shouldDirty: true, shouldValidate: false, shouldTouch: false });
        setQuestionValues(prev => ({
          ...prev,
          [field]: newFieldCount.toString()
        }));
      });

      setCountsIfChanged(adjustedCounts);
    } else {
      // If there are no other fields with values, adjust the primary field
      // This case implies the primaryField itself caused the excess and must absorb it.
      const cappedPrimaryCount = Math.max(0, adjustedCounts[primaryField] - excess);
      adjustedCounts[primaryField] = cappedPrimaryCount;
      setValue(primaryField, cappedPrimaryCount.toString(), { shouldDirty: true, shouldValidate: false, shouldTouch: false });
      setQuestionValues(prev => ({
        ...prev,
        [primaryField]: cappedPrimaryCount.toString()
        }));
        setCountsIfChanged(adjustedCounts);
      }
    }, [questionTypeOptions, setValue, setCountsIfChanged]);

  // Helper function to handle count reduction (memoized)
  const handleCountReduction = useCallback((
    fieldName: string,
    newCount: number, // current value of fieldName
    currentCounts: Record<string, number>, // all counts including fieldName's new value
    otherFields: string[]
  ) => {
    // currentTotal already reflects fieldName's newCount
    const currentTotal = Object.values(currentCounts).reduce((sum, count) => sum + count, 0);

    if (currentTotal < totalQuestions) {
      const deficit = totalQuestions - currentTotal;
      const adjustedCounts = { ...currentCounts };

      if (otherFields.length > 0) {
        const baseIncrease = Math.floor(deficit / otherFields.length);
        let remainingIncrease = deficit % otherFields.length; // Use let

        otherFields.forEach((field) => { // Removed index
          const currentFieldCount = adjustedCounts[field];
          const extraIncrease = remainingIncrease > 0 ? 1 : 0;
          if (extraIncrease > 0) remainingIncrease--;
          
          const totalIncrease = baseIncrease + extraIncrease;
          const newFieldCount = currentFieldCount + totalIncrease;
          adjustedCounts[field] = newFieldCount;

          setValue(field, newFieldCount.toString(), { shouldDirty: true, shouldValidate: false, shouldTouch: false });
          setQuestionValues(prev => ({ ...prev, [field]: newFieldCount.toString() }));
        });
        setCountsIfChanged(adjustedCounts);
      } else {
        // No other fields to distribute to, deficit must be added back to the current field
        const finalCount = newCount + deficit;
        adjustedCounts[fieldName] = finalCount;
        setValue(fieldName, finalCount.toString(), { shouldDirty: true, shouldValidate: false, shouldTouch: false });
        setQuestionValues(prev => ({ ...prev, [fieldName]: finalCount.toString() }));
        setCountsIfChanged(adjustedCounts);
      }
    } else {
      // If currentTotal is not less than totalQuestions (i.e., >= totalQuestions)
      // and we are in handleCountReduction, it implies currentTotal must be exactly totalQuestions
      // or something is off. Assuming it's totalQuestions, just update counts.
        setCountsIfChanged(currentCounts);
      }
    }, [totalQuestions, setValue, setCountsIfChanged]);

  // Helper function to handle count increase (memoized)
  const handleCountIncrease = useCallback((
    fieldName: string,
    newCount: number, // desired new count for fieldName
    // prevCount: number, // previous count for fieldName - No longer directly used here
    currentCounts: Record<string, number>, // all counts, with fieldName already updated to newCount
    otherFields: string[] // other fields excluding fieldName
    // prevCountsSnapshot: Record<string, number> // Snapshot of counts before this cycle - No longer needed
  ) => {
    const adjustedCounts = { ...currentCounts }; // currentCounts already has fieldName: newCount

    // The newCount for fieldName is already in adjustedCounts.
    // Calculate the total based on this desired newCount.
    const newTotalBasedOnDesiredIncrease = Object.values(adjustedCounts).reduce((sum, count) => sum + count, 0);

    if (newTotalBasedOnDesiredIncrease > totalQuestions) {
      const excessToDistribute = newTotalBasedOnDesiredIncrease - totalQuestions;
      redistributeExcess(adjustedCounts, fieldName, excessToDistribute);
    } else {
      setCountsIfChanged(adjustedCounts);
    }
  }, [totalQuestions, redistributeExcess, setCountsIfChanged]);

  // Initialize the state with the current form values or default values
  useEffect(() => {
    const initialValues: Record<string, string> = {};
    const initialPercentages: Record<string, number> = {};
    const initialCounts: Record<string, number> = {};

    // Set default distribution if no values exist
    let remainingCount = totalQuestions;
    const optionsCount = questionTypeOptions.length;
    // Lock the inital state for 'Fill Blank Creative Writing'
    let nonZeroOptions = optionsCount;


    // First pass: check stored values and count non-zero options
    questionTypeOptions.forEach(({ value }) => {
      const storedValue = getValues(value);
      if (storedValue) {
        const count = parseInt(storedValue, 10);
        if (count === 0) {
          nonZeroOptions--;
        }
        initialCounts[value] = count;
        initialValues[value] = count.toString();
        remainingCount -= count;
      }
    });

    // If we don't have stored values, distribute evenly
    if (Object.keys(initialValues).length === 0) {
      const baseCount = Math.floor(totalQuestions / optionsCount);
      const remainder = totalQuestions % optionsCount;

      questionTypeOptions.forEach(({ value }, index) => {
        // Add one extra to some items if there's a remainder
        const count = baseCount + (index < remainder ? 1 : 0);
        initialCounts[value] = count;
        initialValues[value] = count.toString();
        setValue(value, count.toString());
      });
    } else if (remainingCount !== 0) {

      // If we have stored values but they don't add up to totalQuestions,
      // distribute the remaining count among non-zero options
      if (nonZeroOptions === 0) nonZeroOptions = optionsCount; // If all are zero, distribute to all
      
      const additionalPerOption = Math.floor(remainingCount / INITIAL_NON_ZERO_COUNT);
      let additionalRemainder = remainingCount % nonZeroOptions;
      questionTypeOptions.forEach(({ value,label }) => {
        const currentCount = initialCounts[value] || 0;
        // Only add to non-zero options, or to all if all are zero
        if (currentCount > 0 || nonZeroOptions === optionsCount) {
          const extra = additionalRemainder > 0 ? 1 : 0;
          if (extra > 0) additionalRemainder--;
          const newCount = currentCount + additionalPerOption + extra;
          initialCounts[value] = newCount;
          initialValues[value] = newCount.toString();
          setValue(value, newCount.toString());
          if(label === 'Fill Blank' || label === 'Creative Writing'){ 
            // Ensure zero values are properly set
            initialCounts[value] = 0;
            initialValues[value] = '0';
            setValue(value, '0');
          }
        } else {
          // Ensure zero values are properly set
          initialCounts[value] = 0;
          initialValues[value] = '0';
          setValue(value, '0');
        }
      });
    }

    // Calculate percentages based on counts
    questionTypeOptions.forEach(({ value }) => {
      const count = initialCounts[value];
      const percentage = Math.round((count / totalQuestions) * 100);
      initialPercentages[value] = percentage;
    });

    setQuestionValues(initialValues);
    setPercentages(initialPercentages);
    setCountsIfChanged(initialCounts);
  }, [getValues, setValue, questionTypeOptions, totalQuestions, setCountsIfChanged]);

  // Watch for changes in form values
  useEffect(() => {
    // const countsSnapshotForHandler = { ...counts }; // Snapshot for handlers - No longer needed for handleCountIncrease
    const subscription = watch((formValues, { name: changedFieldName, type }) => {
      if (!changedFieldName || !questionTypeOptions.some(option => option.value === changedFieldName)) {
        return; // Not a field we're interested in
      }

     
      if (isDistributingRef.current && type !== 'change') {
        const rawVal = formValues[changedFieldName] || '0';
        const currentCountNum = counts[changedFieldName] || 0;
        const currentCountStr = currentCountNum.toString();
        // Sync internal React state `questionValues` if RHF value matches current numeric state but differs from `questionValues`
        if (rawVal === currentCountStr && (questionValues[changedFieldName] || '0') !== currentCountStr) {
            setQuestionValues(prev => ({ ...prev, [changedFieldName]: currentCountStr }));
        }
        // Exit to prevent re-entrant distribution.
        // This handles cases where setValue from redistributeExcess or sync effect triggers the watch.
        return;
      }

      const rawNewValue = formValues[changedFieldName] || '0';
      let newCount = parseInt(rawNewValue, 10);
      if (isNaN(newCount) || newCount < 0) newCount = 0;
      const prevCount = counts[changedFieldName] || 0;

      // Early exit if no actual change that requires distribution
      if (newCount === prevCount && rawNewValue === (questionValues[changedFieldName] || '0')) {
        return;
      }
      
      if (isDistributingRef.current) {
        return;
      }

      isDistributingRef.current = true;
      try {
        const tempCounts = { ...counts, [changedFieldName]: newCount };
        const countDifference = newCount - prevCount;

        if (countDifference === 0) {
          // Handles cases like "05" vs "5" or invalid input parsing to same number.
          // Ensure RHF value and questionValues reflect the normalized numeric value.
          const normalizedStringCount = prevCount.toString();
          if (rawNewValue !== normalizedStringCount) {
            // Use non-validating setValue to prevent loops
            setValue(changedFieldName, normalizedStringCount, { shouldDirty: type === 'change', shouldValidate: false, shouldTouch: type === 'change' });
          }
          if ((questionValues[changedFieldName] || '0') !== normalizedStringCount) {
            setQuestionValues(prev => ({ ...prev, [changedFieldName]: normalizedStringCount }));
          }
          return; // No change in numeric counts, no redistribution needed.
        }

        const otherFieldNames = questionTypeOptions
          .filter(option => option.value !== changedFieldName)
          .map(option => option.value);

        if (countDifference < 0) { // Reduction
          // Pass all other fields, handleCountReduction will distribute deficit appropriately
          handleCountReduction(changedFieldName, newCount, tempCounts, otherFieldNames);
        } else { // Increase
          handleCountIncrease(changedFieldName, newCount, tempCounts, otherFieldNames);
        }
        // Handlers call setCounts. Sync effect (useEffect on counts) will update RHF values if needed.
      } finally {
        isDistributingRef.current = false;
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, counts, questionValues, questionTypeOptions, totalQuestions, setValue, handleCountIncrease, handleCountReduction, getValues]);

  // Effect to update percentages whenever 'counts' state changes
  useEffect(() => {
    updatePercentages(counts);
  }, [counts, updatePercentages]);

  // Effect to synchronize RHF form values and questionValues state with the `counts` state
  useEffect(() => {
    let newQuestionValuesLocalChanged = false;
    const newQuestionValuesLocal: Record<string, string> = {};

    questionTypeOptions.forEach(({ value: fieldName }) => {
      const currentNumericCount = counts[fieldName] || 0;
      const currentStringCount = currentNumericCount.toString();
      newQuestionValuesLocal[fieldName] = currentStringCount;

      if ((questionValues[fieldName] || '0') !== currentStringCount) {
        newQuestionValuesLocalChanged = true;
      }

      const rhfValue = getValues(fieldName);
      if (rhfValue !== currentStringCount) {
        setValue(fieldName, currentStringCount, { shouldDirty: false, shouldValidate: false });
      }
    });
    
    if (newQuestionValuesLocalChanged) {
      setQuestionValues(newQuestionValuesLocal);
    }

  }, [counts, questionTypeOptions, setValue, getValues, questionValues]); 

 
  const totalCount = Object.values(counts).reduce((sum, count) => sum + count, 0);

  
  useEffect(() => {
    const mainFieldValue = questionTypeOptions.map(option => ({
      type: option.value,
      label: option.label,
      count: counts[option.value] || 0,
      percentage: percentages[option.value] || 0,
    }));
    setValue(name, mainFieldValue, { shouldDirty: true, shouldValidate: true });
  }, [counts, percentages, name, setValue, questionTypeOptions]);


  const handleInputChange = useCallback((fieldName: string, value: string) => {
    setValue(fieldName, value, { shouldDirty: true, shouldValidate: true });
  }, [setValue]);

  const handleSliderChange = useCallback((fieldName: string, value: number) => {
    const adjustedValue = Math.max(0, value); 

    setValue(fieldName, adjustedValue.toString(), { shouldDirty: true, shouldValidate: false, shouldTouch: true });
  }, [setValue]);

  return {
    questionValues,
    percentages,
    counts,
    totalCount,
    handleInputChange,
    handleSliderChange
  };
};
