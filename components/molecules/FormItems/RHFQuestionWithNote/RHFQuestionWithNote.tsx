import {Controller, useFormContext} from 'react-hook-form';

import {Input} from '@/components/atoms/Input/Input';
import React, { useEffect } from 'react'; // Removed useState
import { useQuestionDistribution } from './useQuestionDistribution'; // Import the hook

const defaultQuestionTypeOptions = [
  { value: 'fill_blank', label: 'Fill in the Blank', note: 0 },
  { value: 'single_choice', label: 'Single Choice', note: 0 },
  { value: 'multiple_choices', label: 'Multiple Choices', note: 0 },
  { value: 'creative_writing', label: 'Creative Writing', note: 0 },
];

type QuestionTypeOption = {
  value: string;
  label: string;
  note: number;
  count?: number;
  percentage?: number;
};

type RHFQuestionWithNoteProps = {
  name?: string;
  options?: {
    type?: {
      questionTypes?: QuestionTypeOption[];
      totalQuestions?: number;
    };
  };
  question?: string;
  hidden?: boolean;
};

export const RHFQuestionWithNote: React.FC<RHFQuestionWithNoteProps> = ({
  name = 'questionTypes',
  options,
  question = 'What type of questions do you want to practice?',
  hidden = false,
  ...restProps
}) => {
  const {
    control,
    // formState: { errors }, // errors not used
    setValue,
    getValues,
    watch,
  } = useFormContext();

  // Get question types from options or use default
  const questionTypeOptions = options?.type?.questionTypes || defaultQuestionTypeOptions;
  

  // Use the totalQuestions from props, defaulting to 20 if not provided
  const totalQuestions = options?.type?.totalQuestions ?? 20;

  const {
    questionValues,
    percentages,
    counts,
    totalCount,
    handleInputChange,
    handleSliderChange,
  } = useQuestionDistribution({
    questionTypeOptions,
    totalQuestions,
    setValue,
    getValues,
    watch,
    name, // Pass the main field name to the hook
  });

  // Render the question types UI
  const renderQuestionTypes = () => {
    return (
      <div className="bg-white rounded-lg p-6 flex flex-col gap-6 w-full border border-[#DDE1E6]" style={{ padding: '24px' }}>
        {/* Header */}
        <div className="flex flex-col gap-2 w-full">
          <h2 className="font-inter text-xl font-semibold leading-[1.5em] text-black">
            {question}
          </h2>
          <p className="font-inter text-base font-normal leading-[1.5em] text-[#747474]">
            Distribute the types of questions you want to include. Total must add up to 100%.
          </p>
        </div>

        {/* Question Type Grid */}
        <div className="flex flex-col gap-6 w-full" style={{ gap: '24px' }}>
          {/* First Row */}
          <div className="flex justify-between w-full" style={{ gap: '24px' }}>
            {questionTypeOptions.slice(0, 2).map(({ label, value }) => {
              const fieldName = `${value}`;
              const percentage = percentages[fieldName] || 0;
              const count = counts[fieldName] || 0;

              return (
                <div className="flex flex-col w-full" key={fieldName} style={{ gap: '16px' }}>
                  <div className="flex justify-between items-center w-full">
                    <span className="font-inter text-base font-normal leading-[1.5em] text-black">{label}</span>
                    <div className="flex items-center gap-4">
                      <div className="flex justify-center items-center border border-[#DDE1E6] rounded-lg w-16" style={{ padding: '4px 8px', borderRadius: '8px' }}>
                        <Input
                          value={questionValues[fieldName] || '0'}
                          name={fieldName}
                          min={0}
                          max={totalQuestions}
                          type="number"
                          className="w-full font-inter text-base font-medium leading-[1.5em] text-[#747474] text-center outline-none border-none p-0 h-6"
                          onChange={(e) => handleInputChange(fieldName, e.target.value)}
                        />
                      </div>
                      <span className="font-inter text-base font-normal leading-[1.5em] text-[#747474] text-right w-12">
                        {percentage}%
                      </span>
                    </div>
                  </div>

                  {/* Slider with Pin */}
                  <div className="relative py-2 w-full">
                    {/* Background track */}
                    <div className="absolute top-1/2 left-0 w-full h-2 -translate-y-1/2 bg-[#DDE1E6] rounded-[50px]"></div>

                    {/* Progress fill */}
                    <div
                      className="absolute top-1/2 left-0 h-2 -translate-y-1/2 bg-[#3872FA] rounded-l-[15px]"
                      style={{ width: `${percentage}%` }}
                    ></div>

                    {/* Range input */}
                    <input
                      type="range"
                      min={0}
                      max={totalQuestions}
                      value={count}
                      onChange={(e) => handleSliderChange(fieldName, parseInt(e.target.value, 10))}
                      className="w-full h-8 appearance-none bg-transparent cursor-pointer relative z-10"
                      style={{
                        WebkitAppearance: 'none',
                        appearance: 'none',
                      }}
                    />

                    {/* Handle */}
                    <div
                      className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-[0px_0px_6px_0px_rgba(0,0,0,0.2)] z-20 pointer-events-none"
                      style={{ left: `calc(${percentage}% - ${percentage > 0 ? '8px' : '0px'})` }}
                    ></div>

                    <style>{`
                      input[type="range"] {
                        -webkit-appearance: none;
                        appearance: none;
                        background: transparent;
                        height: 16px;
                      }

                      input[type="range"]::-webkit-slider-thumb {
                        -webkit-appearance: none;
                        appearance: none;
                        width: 16px;
                        height: 16px;
                        background: transparent;
                        border: none;
                        cursor: pointer;
                      }

                      input[type="range"]::-moz-range-thumb {
                        width: 16px;
                        height: 16px;
                        background: transparent;
                        border: none;
                        cursor: pointer;
                      }

                      input[type="range"]:focus {
                        outline: none;
                      }
                    `}</style>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Second Row */}
          <div className="flex justify-between w-full" style={{ gap: '24px' }}>
            {questionTypeOptions.slice(2, 4).map(({ label, value }) => {
              const fieldName = `${value}`;
              const percentage = percentages[fieldName] || 0;
              const count = counts[fieldName] || 0;

              return (
                <div className="flex flex-col w-full" key={fieldName} style={{ gap: '16px' }}>
                  <div className="flex justify-between items-center w-full">
                    <span className="font-inter text-base font-normal leading-[1.5em] text-black">{label}</span>
                    <div className="flex items-center gap-4">
                      <div className="flex justify-center items-center border border-[#DDE1E6] rounded-lg w-16" style={{ padding: '4px 8px', borderRadius: '8px' }}>
                        <Input
                          value={questionValues[fieldName] || '0'}
                          name={fieldName}
                          min={0}
                          max={totalQuestions}
                          type="number"
                          className="w-full font-inter text-base font-medium leading-[1.5em] text-[#747474] text-center outline-none border-none p-0 h-6"
                          onChange={(e) => handleInputChange(fieldName, e.target.value)}
                        />
                      </div>
                      <span className="font-inter text-base font-normal leading-[1.5em] text-[#747474] text-right w-12">
                        {percentage}%
                      </span>
                    </div>
                  </div>

                  {/* Slider with Pin */}
                  <div className="relative py-2 w-full">
                    {/* Background track */}
                    <div className="absolute top-1/2 left-0 w-full h-2 -translate-y-1/2 bg-[#DDE1E6] rounded-[50px]"></div>

                    {/* Progress fill */}
                    <div
                      className="absolute top-1/2 left-0 h-2 -translate-y-1/2 bg-[#3872FA] rounded-l-[15px]"
                      style={{ width: `${percentage}%` }}
                    ></div>

                    {/* Range input */}
                    <input
                      type="range"
                      min={0}
                      max={totalQuestions}
                      value={count}
                      onChange={(e) => handleSliderChange(fieldName, parseInt(e.target.value, 10))}
                      className="w-full h-8 appearance-none bg-transparent cursor-pointer relative z-10"
                      style={{
                        WebkitAppearance: 'none',
                        appearance: 'none',
                      }}
                    />

                    {/* Handle */}
                    <div
                      className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-[0px_0px_6px_0px_rgba(0,0,0,0.2)] z-20 pointer-events-none"
                      style={{ left: `calc(${percentage}% - ${percentage > 0 ? '8px' : '0px'})` }}
                    ></div>

                    <style>{`
                      input[type="range"] {
                        -webkit-appearance: none;
                        appearance: none;
                        background: transparent;
                        height: 16px;
                      }

                      input[type="range"]::-webkit-slider-thumb {
                        -webkit-appearance: none;
                        appearance: none;
                        width: 16px;
                        height: 16px;
                        background: transparent;
                        border: none;
                        cursor: pointer;
                      }

                      input[type="range"]::-moz-range-thumb {
                        width: 16px;
                        height: 16px;
                        background: transparent;
                        border: none;
                        cursor: pointer;
                      }

                      input[type="range"]:focus {
                        outline: none;
                      }
                    `}</style>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <p className={`font-inter text-base font-normal leading-[1.5em] ${totalCount === totalQuestions ? 'text-[#747474]' : 'text-red-500 font-medium'}`}>
          Total: {totalCount} Questions ({Object.values(percentages).reduce((sum, p) => sum + p, 0)}%)
          {totalCount !== totalQuestions && ` (Should be ${totalQuestions})`}
        </p>
      </div>
    );
  };


  return (
    <Controller
      control={control}
      name={name} 
      render={({ field, fieldState, formState }) => {
        if (hidden) return (
          <div style={{ display: 'none' }}></div>
        );
        return (
          <div className="w-full">
            {renderQuestionTypes()}
          </div>
        );
      }}
    />
  );
};
