import {Controller, useFormContext} from 'react-hook-form';
import React, {useEffect, useRef, useState} from 'react';

import {cn} from '@/utils/cn';
// Assuming an Input component might be available, else use raw input
// import Input from '@/components/atoms/Input/Input';

const hideNumberInputSpinners = `
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
    -moz-appearance: textfield; /* Firefox */
  }
`;

export type TRHFQuestionOption = {
  label: string;
  value: string;
  isActive?: boolean;
};

export type TRHFQuestionValue = { // Type for the field's value
  value: string; // The selected option's value (e.g., "10", "20", "custom")
  label: string; // The selected option's label
  isCustom?: boolean; // True if 'custom' is selected
  customValue?: number | string; // The actual custom number input by user
};

export type TRHFQuestionProps = {
  name: string;
  question: string;
  options: TRHFQuestionOption[];
  // Add a prop to specifically enable custom input logic for question_count
  // This makes the component more reusable and explicit
  enableCustomInput?: boolean; 
}

export const RHFQuestion: React.FC<TRHFQuestionProps> = ({
  name,
  question,
  options,
  enableCustomInput = false, // Default to false
}) => {
  const { control, watch, setValue } = useFormContext();
  const fieldValue = watch(name) as TRHFQuestionValue | string | undefined;
  const customInputRef = useRef<HTMLInputElement>(null);
  const [isCustomInputFocused, setIsCustomInputFocused] = useState(false);

  // Effect to clear customValue if a non-custom option is selected
  useEffect(() => {
    if (enableCustomInput && fieldValue && typeof fieldValue === 'object' && fieldValue.value !== 'custom' && fieldValue.customValue !== undefined) {
      const { customValue, ...restValue } = fieldValue; // Create new object without customValue
      setValue(name, restValue, { shouldValidate: true, shouldDirty: true });
    }
  }, [fieldValue, name, setValue, enableCustomInput]);

  // Effect to auto-focus custom input when it becomes visible
  useEffect(() => {
    if (enableCustomInput && fieldValue && typeof fieldValue === 'object' && fieldValue.value === 'custom' && customInputRef.current) {
      customInputRef.current.focus();
    }
  }, [enableCustomInput, fieldValue]); // Depend on the whole fieldValue object to re-evaluate when customValue changes too

  return (
    <Controller
      control={control}
      name={name}
      render={({
        field: { value: controllerValue, onChange, ...restField },
        fieldState: { error },
      }) => {
        const currentSelectedOptionValue = typeof controllerValue === 'object' && controllerValue !== null
          ? controllerValue.value
          : controllerValue;
        
        const currentFullFieldValue = typeof controllerValue === 'object' && controllerValue !== null ? controllerValue : undefined;

        return (
          <> {/* Fragment to hold style and div */}
            <style>{hideNumberInputSpinners}</style>
            <div className="flex flex-col gap-6 border rounded-lg border-gray-200 p-6">
              <div className="text-xl font-semibold select-none w-fit">
                {question}
              </div>
            <div className="grid grid-cols-3 gap-6">
              {options?.map(({ label, value: optionValue, isActive }) => {
                const isCustomOptionSelected = enableCustomInput && currentSelectedOptionValue === 'custom' && optionValue === 'custom';
                return (
                  <fieldset
                    key={`${name || ''}-${String(currentSelectedOptionValue)}-${optionValue}`}
                    className={cn(
                      "fieldset w-full flex flex-col bg-base-100 border-base-300 rounded-box border p-4 transition-all duration-200", // Added transition
                      !isActive && "opacity-50 cursor-not-allowed",
                      // isCustomOptionSelected && "row-span-2", // Removed for simplicity, height will be natural
                      isCustomOptionSelected && isCustomInputFocused && "border-primary ring-2 ring-primary ring-offset-1" // Enhanced focus style for the box
                    )}
                    // disabled={!isActive && optionValue !== 'custom'} // Let's simplify: fieldset disabled only if option is globally inactive
                    disabled={!isActive}
                  >
                    <legend className="fieldset-legend"></legend>
                    <label className={cn("label flex justify-between w-full text-accent font-medium text-2xl", !isActive && "cursor-not-allowed")}>
                      {label}
                      <input
                        type="checkbox"
                        className={cn(
                          'checkbox',
                          'text-white rounded-[50%]',
                          'checked:border-primary checked:bg-primary checked:text-white',
                          'disabled:opacity-70 disabled:cursor-not-allowed'
                        )}
                        checked={optionValue === currentSelectedOptionValue}
                        {...restField}
                        disabled={!isActive} // Checkbox disabled based on its own isActive
                        onChange={(e) => {
                          if (!isActive) return;
                          if (e.target.checked) {
                            if (enableCustomInput) {
                              const newValuePayload: TRHFQuestionValue = {
                                value: optionValue,
                                label: label,
                                isCustom: optionValue === 'custom',
                              };
                              if (optionValue === 'custom') {
                                // Preserve existing customValue if "Custom" is re-selected, else init
                                newValuePayload.customValue = (currentFullFieldValue?.isCustom && currentFullFieldValue.customValue) 
                                                              ? currentFullFieldValue.customValue 
                                                              : '';
                              }
                              onChange(newValuePayload);
                            } else {
                              onChange({ value: optionValue, label: label });
                            }
                          } else {
                            onChange(enableCustomInput ? undefined : '');
                          }
                        }}
                      />
                    </label>
                    {isCustomOptionSelected && (
                      <div className="mt-2 w-full">
                        {/* <label htmlFor={`${name}-custom-input`} className="block text-sm font-medium text-gray-700 mb-1 sr-only">
                          Custom Question Count
                        </label> */}
                        <input
                          ref={customInputRef}
                          type="number"
                          id={`${name}-custom-input`}
                          name={`${name}-custom-input`}
                          className="input w-full text-xl py-2 focus:outline-none" // Added focus:outline-none
                          value={currentFullFieldValue?.customValue || ''}
                          onChange={(e) => {
                            const numValue = e.target.value;
                            onChange({
                              ...(currentFullFieldValue as TRHFQuestionValue),
                              customValue: numValue ? parseInt(numValue, 10) : '',
                            });
                          }}
                          placeholder="Enter count"
                          min="1"
                          onClick={(e) => e.stopPropagation()}
                          onFocus={() => setIsCustomInputFocused(true)}
                          onBlur={() => setIsCustomInputFocused(false)}
                        />
                        {error && currentFullFieldValue?.value === 'custom' && 
                         (currentFullFieldValue?.customValue === undefined || currentFullFieldValue?.customValue === '') && (
                          <p className="text-error text-xs mt-1">Custom count is required.</p> // Used DaisyUI text-error
                        )}
                      </div>
                    )}
                  </fieldset>
                );
              })}
            </div>
            {/* Old custom input section removed from here */}
            </div>
          </>
        );
      }}
    />
  );
};
