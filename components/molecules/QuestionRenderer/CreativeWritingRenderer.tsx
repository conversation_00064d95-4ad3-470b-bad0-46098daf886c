'use client';

import React, { useState, useCallback } from 'react';
import { cn } from '@/utils/cn';

export interface CreativeWritingRendererProps {
  answer: string[];
  prompt?: string;
  isHtmlContent?: boolean;
  className?: string;
  minWords?: number;
  maxWords?: number;
  rubricCriteria?: string[];
}

export const CreativeWritingRenderer: React.FC<CreativeWritingRendererProps> = ({
  answer,
  prompt,
  isHtmlContent = false,
  className,
  minWords = 50,
  maxWords = 500,
  rubricCriteria = [],
}) => {
  const [userInput, setUserInput] = useState('');
  const [showSample, setShowSample] = useState(false);

  const wordCount = userInput.trim().split(/\s+/).filter(word => word.length > 0).length;
  const characterCount = userInput.length;

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setUserInput(e.target.value);
  }, []);

  const handleClear = useCallback(() => {
    setUserInput('');
  }, []);

  const getWordCountColor = () => {
    if (wordCount < minWords) return 'text-red-500';
    if (wordCount > maxWords) return 'text-red-500';
    return 'text-green-500';
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Prompt */}
      {prompt && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-900 mb-2">Writing Prompt</h3>
          {isHtmlContent ? (
            <div dangerouslySetInnerHTML={{ __html: prompt }} />
          ) : (
            <p className="text-blue-800">{prompt}</p>
          )}
        </div>
      )}

      {/* Writing Area */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <label className="block text-sm font-medium text-gray-700">
            Your Response
          </label>
          <div className="flex gap-4 text-sm">
            <span className={cn('font-medium', getWordCountColor())}>
              Words: {wordCount}/{maxWords}
            </span>
            <span className="text-gray-500">
              Characters: {characterCount}
            </span>
          </div>
        </div>
        
        <textarea
          value={userInput}
          onChange={handleInputChange}
          placeholder="Start writing your response here..."
          className="w-full min-h-[200px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-y"
          maxLength={maxWords * 10} // Rough character limit
        />
        
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Minimum {minWords} words required
          </div>
          <button
            onClick={handleClear}
            className="text-sm text-red-600 hover:text-red-800"
          >
            Clear
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={cn(
            'h-2 rounded-full transition-all duration-300',
            wordCount < minWords ? 'bg-red-400' : 
            wordCount > maxWords ? 'bg-red-400' : 'bg-green-400'
          )}
          style={{
            width: `${Math.min((wordCount / maxWords) * 100, 100)}%`
          }}
        />
      </div>

      {/* Rubric Criteria */}
      {rubricCriteria.length > 0 && (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h4 className="font-semibold text-gray-900 mb-2">Assessment Criteria</h4>
          <ul className="space-y-1">
            {rubricCriteria.map((criteria, index) => (
              <li key={index} className="text-sm text-gray-700 flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                {criteria}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Sample Answer */}
      {answer.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <button
            onClick={() => setShowSample(!showSample)}
            className="w-full p-3 text-left font-medium text-gray-700 hover:bg-gray-50 flex justify-between items-center"
          >
            Sample Answer
            <span className={cn('transform transition-transform', showSample ? 'rotate-180' : '')}>
              ▼
            </span>
          </button>
          
          {showSample && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              {answer.map((sampleAnswer, index) => (
                <div key={index} className="mb-3 last:mb-0">
                  {isHtmlContent ? (
                    <div dangerouslySetInnerHTML={{ __html: sampleAnswer }} />
                  ) : (
                    <p className="text-gray-700">{sampleAnswer}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
