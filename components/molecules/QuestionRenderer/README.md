# Question Renderer Components

Modern, interactive question renderers for educational worksheets built with React, TailwindCSS v4, and DaisyUI v5.

## Components

### FillBlankRenderer

Interactive fill-in-the-blank question renderer with validation and feedback.

**Features:**
- Real-time answer input
- Answer validation and checking
- Show/hide correct answers
- Reset functionality
- Support for multiple blank patterns
- Visual feedback for correct/incorrect answers

**Props:**
```typescript
interface FillBlankRendererProps {
  content: string;           // Question content with blanks (e.g., "The ___ is big")
  answer: string[];          // Array of correct answers
  isHtmlContent?: boolean;   // Whether content contains HTML
  className?: string;        // Additional CSS classes
}
```

**Example:**
```tsx
<FillBlankRenderer
  content="The ___ orbits the ___."
  answer={["Earth", "Sun"]}
  isHtmlContent={false}
/>
```

### CreativeWritingRenderer

Comprehensive creative writing interface with word tracking and guidance.

**Features:**
- Word and character count tracking
- Progress indicators for word count goals
- Writing rubric with assessment criteria
- Sample answer display
- Writing tips and guidelines
- Auto-resizing textarea
- Clear and reset functionality

**Props:**
```typescript
interface CreativeWritingRendererProps {
  answer: string[];              // Sample answers
  prompt?: string;               // Writing prompt
  isHtmlContent?: boolean;       // Whether content contains HTML
  className?: string;            // Additional CSS classes
  minWords?: number;             // Minimum word requirement (default: 50)
  maxWords?: number;             // Maximum word limit (default: 500)
  rubricCriteria?: string[];     // Assessment criteria
}
```

**Example:**
```tsx
<CreativeWritingRenderer
  answer={["Sample creative writing..."]}
  prompt="Describe your ideal vacation"
  minWords={100}
  maxWords={300}
  rubricCriteria={[
    "Creativity and originality",
    "Use of descriptive language",
    "Grammar and spelling"
  ]}
/>
```

## Styling

Both components use DaisyUI v5 design tokens and are fully responsive:

- **Cards**: Modern card layouts with shadows and borders
- **Forms**: Styled inputs and textareas with focus states
- **Buttons**: Semantic button variants (primary, secondary, outlined)
- **Alerts**: Contextual feedback with icons
- **Badges**: Status indicators and labels
- **Progress**: Visual progress tracking

## Accessibility

- Screen reader compatible
- Keyboard navigation support
- Proper ARIA labels and roles
- High contrast color support
- Focus management

## Browser Support

- Modern browsers with ES2020 support
- Mobile Safari and Chrome
- Responsive touch interactions

## Dependencies

- React 19+
- TailwindCSS v4
- DaisyUI v5
- Lucide React (for icons)

## Development

All components are written in TypeScript with full type safety and include comprehensive error handling and validation.
