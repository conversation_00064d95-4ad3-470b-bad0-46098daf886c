'use client';

import React from 'react';
import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';

const QuestionRendererDemo: React.FC = () => {
  const sampleQuestions: Question[] = [
    {
      type: 'fill_blank',
      content: 'The ___ is the largest planet in our ___. It has more than ___ moons and is mostly made of ___ and ___.',
      answer: ['Jupiter', 'solar system', '80', 'hydrogen', 'helium'],
      explain: 'Jupiter is indeed the largest planet in our solar system, with over 80 known moons. It is a gas giant composed primarily of hydrogen and helium.',
      options: [],
      image: null,
    },
    {
      type: 'creative_writing',
      content: 'Write a short story about a character who discovers a magical door in their backyard.',
      prompt: 'Imagine you are walking in your backyard when you notice something unusual behind the old oak tree. Write a creative story of at least 100 words about what happens next. Include descriptive details about what you see, hear, and feel.',
      answer: [
        'As I stepped behind the ancient oak tree, I gasped. There, nestled between two gnarled roots, stood a door no bigger than a doghouse. Its surface shimmered with an iridescent purple hue, and intricate silver vines twisted around its edges like living metal.\n\nMy heart pounded as I reached for the tiny brass handle. The moment my fingers touched it, the door began to glow. Suddenly, I felt myself shrinking, the world around me growing massive. The grass became a jungle, and dewdrops turned into crystal lakes.\n\nWith trembling hands, I pushed open the door and stepped into a world where butterflies sang opera and flowers danced in the breeze. It was then I realized that some adventures begin right in your own backyard.'
      ],
      explain: 'This is a creative writing exercise designed to encourage imagination and descriptive writing. Students should focus on creating vivid imagery and developing a compelling narrative.',
      options: [],
      image: null,
    },
    {
      type: 'multiple_choice',
      content: 'Which of the following are correct statements about photosynthesis?',
      answer: [
        'Plants use sunlight to make food',
        'Oxygen is produced as a by-product',
        'It occurs mainly in leaves'
      ],
      explain: 'Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen. This occurs primarily in the leaves where chloroplasts are concentrated.',
      options: [
        'Plants use sunlight to make food',
        'Plants only photosynthesize at night',
        'Oxygen is produced as a by-product',
        'It occurs mainly in leaves',
        'Carbon dioxide is produced during photosynthesis'
      ],
      image: null,
    },
    {
      type: 'single_choice',
      content: 'What is the capital of Australia?',
      answer: ['Canberra'],
      explain: 'Canberra is the capital city of Australia. It was purpose-built as the national capital and is located between Sydney and Melbourne.',
      options: [
        'Sydney',
        'Melbourne',
        'Canberra',
        'Perth',
        'Brisbane'
      ],
      image: null,
    },
    // Add more questions for better testing of sticky behavior
    {
      type: 'fill_blank',
      content: 'Water boils at ___ degrees Celsius at ___ atmospheric pressure.',
      answer: ['100', 'standard'],
      explain: 'Water boils at 100°C (212°F) at standard atmospheric pressure (1 atmosphere or 14.7 psi).',
      options: [],
      image: null,
    },
    {
      type: 'creative_writing',
      content: 'Describe a day in the life of a time traveler.',
      prompt: 'You wake up in a different time period every morning. Today you find yourself in ancient Rome. Write about your experiences, challenges, and discoveries throughout the day.',
      answer: [
        'I awakened to the sound of chariot wheels on cobblestone, the acrid smell of burning olive oil filling my nostrils. Through the window, I could see the mighty Colosseum in the distance, its limestone walls gleaming in the morning sun.\n\nThe challenge began immediately - understanding Latin was one thing, but navigating the complex social hierarchy of Roman society was another. I had to be careful not to reveal my true origins while exploring this magnificent civilization.\n\nI spent the day wandering through the Forum, watching senators debate, listening to poets recite their verses, and marveling at the advanced engineering of the aqueducts. By evening, as I watched gladiators train for their next bout, I couldn\'t help but wonder what tomorrow would bring - perhaps medieval England, or maybe ancient Egypt.'
      ],
      explain: 'This exercise encourages historical imagination and descriptive writing about different time periods.',
      options: [],
      image: null,
    }
  ];

  const worksheetInfo = {
    topic: 'Science & Creative Writing Mix',
    grade: '6th Grade',
    language: 'English',
    level: 'Intermediate',
    totalQuestions: sampleQuestions.length,
  };

  return (
    <div className="min-h-screen bg-base-200">
      {/* Fixed header to test sticky behavior */}
      <div className="bg-base-100 shadow-sm border-b border-base-300 p-4">
        <h1 className="text-3xl font-bold text-center text-base-content">
          Enhanced Question Renderer Demo
        </h1>
        <p className="text-center text-base-content/70 mt-2">
          Scroll down to see the sticky worksheet header in action
        </p>
      </div>
      
      {/* Main content with scrollable area */}
      <div className="max-w-4xl mx-auto">
        <div className="card bg-base-100 shadow-lg border border-base-300 m-6">
          <div className="card-body">
            <h2 className="card-title text-primary">✨ Enhanced Features</h2>
            <ul className="text-sm space-y-2">
              <li>🎯 <strong>Fill in the Blanks:</strong> Interactive input fields with validation and answer reveal</li>
              <li>✍️ <strong>Creative Writing:</strong> Word count tracking, sample answers, and writing rubric</li>
              <li>🎨 <strong>Improved Design:</strong> Modern DaisyUI components with TailwindCSS v4</li>
              <li>📌 <strong>Sticky Header:</strong> Worksheet info stays visible while scrolling</li>
              <li>📱 <strong>Responsive:</strong> Mobile-friendly layout with touch-optimized controls</li>
              <li>♿ <strong>Accessible:</strong> Screen reader friendly with proper ARIA labels</li>
            </ul>
          </div>
        </div>

        {/* Questions with sticky header */}
        <div className="bg-base-100 m-6 rounded-lg overflow-hidden shadow-lg">
          <QuestionListingView
            questions={sampleQuestions}
            worksheetInfo={worksheetInfo}
            isHtmlContent={false}
          />
        </div>
      </div>
    </div>
  );
};

export default QuestionRendererDemo;
