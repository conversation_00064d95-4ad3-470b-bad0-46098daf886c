'use client';

import React from 'react';
import { FileText, CheckCircle, Clock, AlertTriangle } from 'lucide-react';

interface NarrativeStructurePreviewProps {
  status?: string;
  onClick: () => void;
}

export const NarrativeStructurePreview: React.FC<NarrativeStructurePreviewProps> = ({
  status,
  onClick,
}) => {
  return (
    <div 
      className="w-full md:w-1/3 bg-white p-3 rounded-lg border border-gray-200 flex items-center justify-center cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all shadow-sm"
      onClick={onClick}
      role="button"
      aria-label="View narrative structure"
      tabIndex={0}
      onKeyDown={(e) => e.key === 'Enter' && onClick()}
    >
      <div className="relative w-full h-full min-h-[120px] flex items-center justify-center">
        <div className="w-20 h-28 bg-white border border-gray-200 rounded-md shadow-md flex items-center justify-center transform transition-transform hover:scale-105">
          <FileText size={24} className="text-blue-500" />
        </div>
        <div className="absolute -bottom-1 -right-1 transform translate-x-1/4 translate-y-1/4">
          {status === 'COMPLETED' && (
            <div className="text-green-500 bg-white p-1.5 rounded-full border border-green-100 shadow-md">
              <CheckCircle size={16} />
            </div>
          )}
          {(status === 'PENDING' || status === 'PROCESSING') && (
            <div className="text-amber-500 bg-white p-1.5 rounded-full border border-amber-100 shadow-md">
              <Clock size={16} />
            </div>
          )}
          {status === 'FAILED' && (
            <div className="text-red-500 bg-white p-1.5 rounded-full border border-red-100 shadow-md">
              <AlertTriangle size={16} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};