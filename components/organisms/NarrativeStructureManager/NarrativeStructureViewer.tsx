'use client';

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { NarrativeStructurePreview } from './NarrativeStructurePreview';
import { NarrativeStructureDetails } from './NarrativeStructureDetails';
import { NarrativeStructureEmpty } from './NarrativeStructureEmpty';
import { NarrativeStructureDialog } from './NarrativeStructureDialog';
import { getNarrativeStructureAction, extractNarrativeStructureAction } from '@/actions/narrativeStructure.action';

// Helper to handle message formatting (string or array of validation errors)
const formatApiMessage = (message: string | any[] | undefined): string => {
  if (Array.isArray(message)) {
    return message.map(m => typeof m === 'object' && m.message ? m.message : String(m)).join(', ');
  }
  return String(message || '');
};

interface NarrativeStructureViewerProps {
  schoolId: string;
  schoolName: string;
  onExtractSuccess?: () => void;
  onExtractError?: (message: string) => void;
  onDeleteSuccess?: () => void;
  onDeleteError?: (message: string) => void;
}

export const NarrativeStructureViewer: React.FC<NarrativeStructureViewerProps> = ({
  schoolId,
  schoolName,
  onExtractSuccess,
  onExtractError,
  onDeleteSuccess,
  onDeleteError,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isExtracting, setIsExtracting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [structure, setStructure] = useState<{
    id: string;
    schoolId: string;
    content: string;
    status: string;
    extractedBy?: string;
    createdAt: string;
    updatedAt?: string;
  } | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    const fetchStructure = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await getNarrativeStructureAction(schoolId);

        if (response.status === 'success') {
          setStructure(response.data);
        } else {
          // Not setting error here as this is an expected state when no structure exists
          setStructure(null);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch narrative structure');
        setStructure(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStructure();
  }, [schoolId]);

  const handleExtract = async () => {
    setIsExtracting(true);
    setError(null);

    try {
      const response = await extractNarrativeStructureAction(schoolId);

      if (response.status === 'success') {
        // Extract action only returns basic info, so we need to fetch the full structure
        // For now, we'll just trigger a success and let the parent component refresh
        if (onExtractSuccess) {
          onExtractSuccess();
        }
        // Optionally, we could fetch the full structure here
        // const fullResponse = await getNarrativeStructureAction(schoolId);
        // if (fullResponse.status === 'success') {
        //   setStructure(fullResponse.data);
        // }
      } else {
        const errorMessage = formatApiMessage(response.message) || 'Failed to extract narrative structure';
        setError(errorMessage);
        if (onExtractError) {
          onExtractError(errorMessage);
        }
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to extract narrative structure';
      setError(errorMessage);
      if (onExtractError) {
        onExtractError(errorMessage);
      }
    } finally {
      setIsExtracting(false);
    }
  };

  const handleDelete = async () => {
    if (structure && onDeleteSuccess) {
      onDeleteSuccess();
    }
  };

  const handleViewStructure = () => {
    setIsDialogOpen(true);
  };

  return (
    <div className="w-full">
      {isLoading ? (
        <div className="w-full flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
        </div>
      ) : error ? (
        <div className="w-full p-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
          <p>Error: {error}</p>
        </div>
      ) : !structure ? (
        <NarrativeStructureEmpty
          onExtractClick={handleExtract}
          isExtracting={isExtracting}
        />
      ) : (
        <div className="w-full bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/3 border-b md:border-b-0 md:border-r border-gray-200">
              <NarrativeStructurePreview
                status={structure.status}
                onClick={handleViewStructure}
              />
            </div>
            <div className="md:w-2/3 p-4">
              <NarrativeStructureDetails
                schoolName={schoolName}
                status={structure.status}
                createdAt={structure.createdAt}
                extractedBy={structure.extractedBy}
                onViewClick={handleViewStructure}
                onDeleteClick={handleDelete}
                onExtractClick={handleExtract}
                isExtracting={isExtracting}
              />
            </div>
          </div>
        </div>
      )}

      <NarrativeStructureDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        structure={structure?.content || ''}
      />
    </div>
  );
};