'use client';

import React from 'react';
import { X, FileText } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface NarrativeStructureDialogProps {
  isOpen: boolean;
  onClose: () => void;
  structure: string;
}

export const NarrativeStructureDialog: React.FC<NarrativeStructureDialogProps> = ({
  isOpen,
  onClose,
  structure,
}) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" 
      style={{ zIndex: 9999 }}
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div 
        className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 scale-100"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-bold flex items-center gap-2">
            <FileText size={20} />
            Narrative Structure
          </h2>
          <button 
            onClick={onClose}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6 bg-gray-50">
          {structure ? (
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap bg-white p-5 rounded-lg border border-gray-200 text-sm font-mono shadow-sm overflow-auto">
                {structure}
              </pre>
            </div>
          ) : (
            <div className="text-center p-8 text-gray-500 bg-white rounded-lg border border-gray-200">
              <div className="flex flex-col items-center justify-center gap-3">
                <FileText size={32} className="text-gray-400" />
                <p className="text-gray-600">No narrative structure content available.</p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 flex justify-end bg-white">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-4 py-2"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};