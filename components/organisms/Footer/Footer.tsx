'use client';

import { cn } from '@/utils/cn';
import * as React from 'react';

export interface FooterProps {
  pageContainerType?: 'default' | 'gutterless' | 'contained';
}

const Footer: React.FC<FooterProps> = ({ pageContainerType = 'default' }) => {
  return (
    <footer
      className={cn(
        'py-4 border-t border-gray-200 bg-white',
        pageContainerType === 'contained' && 'container mx-auto px-4',
        pageContainerType === 'gutterless' && 'px-0',
        pageContainerType === 'default' && 'px-4'
      )}
    >
      <div className="text-sm text-gray-500 text-center">
        © {new Date().getFullYear()} Your Company. All rights reserved.
      </div>
    </footer>
  );
};

export default Footer;
