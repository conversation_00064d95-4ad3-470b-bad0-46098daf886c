'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/atoms/Dialog/Dialog';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import {  X} from 'lucide-react';

interface ExaminationFormatDialogProps {
  isOpen: boolean;
  onClose: () => void;
  format: any;
}

export const ExaminationFormatDialog: React.FC<ExaminationFormatDialogProps> = ({
  isOpen,
  onClose,
  format,
}) => {
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    if (isOpen) {
      setError(null);
    }
  }, [isOpen]);


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="border-b pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">
              Examination Format
            </DialogTitle>
            <button
              onClick={onClose}
              className="rounded-full p-1 hover:bg-gray-100 transition-colors"
              aria-label="Close dialog"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </DialogHeader>

        <div className="flex flex-col md:flex-row gap-6 overflow-auto p-4">
          {/* PDF Viewer */}
          <div className="flex-1 min-w-0 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
            {error ? (
              <div className="p-4">
                <AlertMessage type="error" message={error} />
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <div className="w-full max-h-[60vh] overflow-auto p-4">
                  
                  <pre className="whitespace-pre-wrap p-4 bg-white rounded-lg border border-gray-200 text-sm">
                    {format.data}
                  </pre>
                </div>
                
              </div>
            )}
          </div>
        
        </div>
      </DialogContent>
    </Dialog>
  );
};