'use client';

import React from 'react';
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/atoms/Dialog/Dialog';
import { ExaminationFormatUploader } from './ExaminationFormatUploader';
import { X } from 'lucide-react';

interface ExaminationFormatUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  schoolId?: string;
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: string) => void;
  isReplacement?: boolean;
}

export const ExaminationFormatUploadModal: React.FC<ExaminationFormatUploadModalProps> = ({
  isOpen,
  onClose,
  schoolId,
  onUploadSuccess,
  onUploadError,
  isReplacement = false,
}) => {
  const handleUploadSuccess = (response: any) => {
    if (onUploadSuccess) {
      onUploadSuccess(response);
    }
    onClose();
  };

  const handleUploadError = (error: string) => {
    if (onUploadError) {
      onUploadError(error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="border-b pb-4 px-6 pt-6">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">
              {isReplacement ? 'Replace Examination Format' : 'Upload Examination Format'}
            </DialogTitle>
            <button
              onClick={onClose}
              className="rounded-full p-1 hover:bg-gray-100 transition-colors"
              aria-label="Close dialog"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </DialogHeader>
        
        <div className="px-6 py-6">
          <ExaminationFormatUploader
            schoolId={schoolId}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            isReplacement={isReplacement}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};