'use client';

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useOnboarding } from './OnboardingContext';
import { Icon } from '@/components/atoms';
import { FileUpload } from '@/components/molecules/FileUpload/FileUpload';
import { handleFileUploadAction } from '@/actions/file.action';
import { handleCreateBrandAction } from '@/actions/brand.action';
import { updateMySchool } from '@/actions/school.action';

// Validation schema for colors
const colorSchema = z.string().regex(/^#([0-9A-Fa-f]{3}){1,2}$/, { message: 'Invalid color hex code' });

// File validation
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];

export default function BrandingStep() {
  const router = useRouter();
  const {
    setBrandingData,
    markStepCompleted,
    getPreviousStep,
    getNextStep
  } = useOnboarding();

  // Local state for branding data
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [brandImageFile, setBrandImageFile] = useState<File | null>(null);
  const [brandImagePreview, setBrandImagePreview] = useState<string | null>(null);
  const [primaryColor, setPrimaryColor] = useState<string>('#3B82F6');
  const [secondaryColor, setSecondaryColor] = useState<string>('#8B5CF6');

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingBrandImage, setUploadingBrandImage] = useState(false);

  // File validation helper
  const validateFile = (file: File): string | null => {
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, or SVG)';
    }
    if (file.size > MAX_FILE_SIZE) {
      return 'File size must be less than 2MB';
    }
    return null;
  };

  // Logo upload handler
  const handleLogoUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setLogoFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setLogoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setError(null);
  }, []);

  // Brand image upload handler
  const handleBrandImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setBrandImageFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setBrandImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setError(null);
  }, []);

  // Clear logo
  const handleClearLogo = useCallback(() => {
    setLogoFile(null);
    setLogoPreview(null);
  }, []);

  // Clear brand image
  const handleClearBrandImage = useCallback(() => {
    setBrandImageFile(null);
    setBrandImagePreview(null);
  }, []);

  // Color change handlers
  const handlePrimaryColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPrimaryColor(e.target.value);
  };

  const handleSecondaryColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSecondaryColor(e.target.value);
  };

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate colors
      const primaryColorValidation = colorSchema.safeParse(primaryColor);
      const secondaryColorValidation = colorSchema.safeParse(secondaryColor);

      if (!primaryColorValidation.success || !secondaryColorValidation.success) {
        setError('Please select valid colors');
        return;
      }

      let logoUrl: string | null = null;
      let brandImageUrl: string | null = null;

      // Upload logo if provided
      if (logoFile) {
        setUploadingLogo(true);

        const logoUploadResult = await handleFileUploadAction(logoFile);
        if (logoUploadResult.status === 'success' && logoUploadResult.data) {
          logoUrl = logoUploadResult.data.url;
        } else {
          throw new Error('Failed to upload logo');
        }
        setUploadingLogo(false);
      }

      // Upload brand image if provided
      if (brandImageFile) {
        setUploadingBrandImage(true);

        const imageUploadResult = await handleFileUploadAction(brandImageFile);
        if (imageUploadResult.status === 'success' && imageUploadResult.data) {
          brandImageUrl = imageUploadResult.data.url;
        } else {
          throw new Error('Failed to upload brand image');
        }
        setUploadingBrandImage(false);
      }

      // Create brand
      const brandPayload = {
        logo: logoUrl || undefined,
        color: primaryColor,
        image: brandImageUrl || undefined,
      };

      const brandResult = await handleCreateBrandAction(brandPayload);
      if (brandResult.status !== 'success' || !brandResult.data) {
        throw new Error('Failed to create brand');
      }

      const brandId = brandResult.data.id;

      // Associate brand with school
      const schoolFormData = new FormData();
      schoolFormData.append('brandId', brandId);

      const schoolUpdateResult = await updateMySchool(schoolFormData);
      if (schoolUpdateResult.status !== 'success') {
        throw new Error('Failed to associate brand with school');
      }

      // Store branding data in context
      setBrandingData({
        primaryColor,
        secondaryColor,
        logoFile,
        logoUrl,
        brandImageFile,
        brandImageUrl,
      });

      // Mark step as completed
      markStepCompleted('branding');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error: any) {
      console.error('Error saving branding data:', error);
      setError(error.message || 'An unexpected error occurred while saving your branding');
    } finally {
      setIsLoading(false);
      setUploadingLogo(false);
      setUploadingBrandImage(false);
    }
  };

  const handleSkip = () => {
    // Skip branding step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Card Container */}
      <div className="card bg-base-100 shadow-xl border border-gray-100">
        <div className="card-body p-8">
          <h2 className="card-title text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3 justify-center">
            <Icon variant="star" size={8} className="text-primary" />
            Customize Your School Branding
          </h2>

          {/* Error Display */}
          {error && (
            <div className="alert alert-error mb-6">
              <Icon variant="alert-triangle" size={5} />
              <span>{error}</span>
            </div>
          )}

          <div className="space-y-8">
            {/* Logo Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Icon variant="upload" size={5} className="text-primary" />
                School Logo
              </h3>
              <p className="text-sm text-gray-600">Upload your school logo (optional)</p>

              <FileUpload
                id="logo-upload"
                accept="image/*"
                preview={logoPreview}
                onUpload={handleLogoUpload}
                onClear={handleClearLogo}
                label="Click to upload logo"
                maxSize="MAX. 2MB"
                disabled={uploadingLogo || isLoading}
              />

              {uploadingLogo && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Uploading logo...</span>
                </div>
              )}
            </div>

            {/* Brand Image Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Icon variant="upload" size={5} className="text-primary" />
                Brand Image
              </h3>
              <p className="text-sm text-gray-600">Upload a brand image or banner (optional)</p>

              <FileUpload
                id="brand-image-upload"
                accept="image/*"
                preview={brandImagePreview}
                onUpload={handleBrandImageUpload}
                onClear={handleClearBrandImage}
                label="Click to upload brand image"
                maxSize="MAX. 2MB"
                disabled={uploadingBrandImage || isLoading}
              />

              {uploadingBrandImage && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Uploading brand image...</span>
                </div>
              )}
            </div>

            {/* Color Selection Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Icon variant="star" size={5} className="text-primary" />
                Brand Colors
              </h3>
              <p className="text-sm text-gray-600">Choose your primary and secondary brand colors</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Primary Color */}
                <div className="space-y-3">
                  <label className="label">
                    <span className="label-text font-medium">Primary Color</span>
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={primaryColor}
                      onChange={handlePrimaryColorChange}
                      className="input input-bordered w-16 h-12 p-1 cursor-pointer"
                      disabled={isLoading}
                    />
                    <input
                      type="text"
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="input input-bordered flex-1"
                      placeholder="#3B82F6"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Secondary Color */}
                <div className="space-y-3">
                  <label className="label">
                    <span className="label-text font-medium">Secondary Color</span>
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={secondaryColor}
                      onChange={handleSecondaryColorChange}
                      className="input input-bordered w-16 h-12 p-1 cursor-pointer"
                      disabled={isLoading}
                    />
                    <input
                      type="text"
                      value={secondaryColor}
                      onChange={(e) => setSecondaryColor(e.target.value)}
                      className="input input-bordered flex-1"
                      placeholder="#8B5CF6"
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Live Preview Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Icon variant="eye" size={5} className="text-primary" />
                Preview
              </h3>
              <div className="card bg-base-100 border-2 border-gray-200 p-6">
                <div className="flex items-center gap-4">
                  <div
                    className="w-12 h-12 rounded-lg shadow-md"
                    style={{ backgroundColor: primaryColor }}
                  ></div>
                  <div
                    className="w-12 h-12 rounded-lg shadow-md"
                    style={{ backgroundColor: secondaryColor }}
                  ></div>
                  <div className="flex-1">
                    <p className="font-medium" style={{ color: primaryColor }}>
                      Primary Color Preview
                    </p>
                    <p className="text-sm" style={{ color: secondaryColor }}>
                      Secondary Color Preview
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleBack}
                disabled={isLoading}
                className="btn btn-outline btn-lg flex items-center gap-2"
              >
                <Icon variant="arrow-left" size={4} />
                <span>Back</span>
              </button>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleSkip}
                  disabled={isLoading}
                  className="btn btn-ghost btn-lg text-gray-600 hover:text-gray-800"
                >
                  Skip
                </button>

                <button
                  type="button"
                  onClick={handleNext}
                  disabled={isLoading || uploadingLogo || uploadingBrandImage}
                  className="btn btn-primary btn-lg flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 shadow-lg"
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>Creating Brand...</span>
                    </>
                  ) : (
                    <>
                      <span>Continue</span>
                      <Icon variant="arrow-right" size={4} />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
