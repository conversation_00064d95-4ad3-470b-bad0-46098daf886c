'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useOnboarding } from './OnboardingContext';
import { Icon } from '@/components/atoms';
import { cn } from '@/utils/cn';

const profileSchema = z.object({
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  specialization: z.string().max(100, 'Specialization must be less than 100 characters').optional(),
  experience: z.string().max(50, 'Experience must be less than 50 characters').optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

const SPECIALIZATION_OPTIONS = [
  'Mathematics',
  'Science',
  'English Language',
  'History',
  'Geography',
  'Art & Design',
  'Music',
  'Physical Education',
  'Computer Science',
  'Languages',
  'Other',
];

const EXPERIENCE_OPTIONS = [
  'New Teacher (0-1 years)',
  'Early Career (2-5 years)',
  'Experienced (6-10 years)',
  'Veteran (11-20 years)',
  'Expert (20+ years)',
];

export default function ProfileStep() {
  const router = useRouter();
  const { 
    setProfileData, 
    markStepCompleted, 
    getPreviousStep,
    getNextStep 
  } = useOnboarding();
  
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      bio: '',
      specialization: '',
      experience: '',
    },
  });

  const watchedSpecialization = watch('specialization');
  const watchedExperience = watch('experience');

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    setIsLoading(true);
    
    try {
      // Store profile data
      setProfileData(data);

      // Mark step as completed
      markStepCompleted('profile');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error) {
      console.error('Error saving profile data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip profile step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Card Container */}
      <div className="card bg-base-100 shadow-xl border border-gray-100">
        <div className="card-body p-8">
          <h2 className="card-title text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3 justify-center">
            <Icon variant="graduation-cap" size={8} className="text-primary" />
            Complete Your Teacher Profile
          </h2>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Bio */}
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base font-semibold text-gray-700">About You</span>
              </label>
              <div className="relative">
                <div className="absolute top-3 left-3 pointer-events-none z-10">
                  <Icon variant="book-open" size={5} className="text-gray-500" />
                </div>
                <textarea
                  placeholder="Tell us about yourself and your teaching philosophy..."
                  className={cn(
                    'textarea textarea-lg textarea-bordered w-full pl-12 pr-4 py-3 min-h-[100px] text-base resize-none',
                    errors.bio
                      ? 'textarea-error'
                      : 'focus:textarea-primary'
                  )}
                  {...register('bio')}
                />
              </div>
              {errors.bio && (
                <label className="label">
                  <span className="label-text-alt text-error font-medium">
                    {errors.bio.message}
                  </span>
                </label>
              )}
            </div>

            {/* Specialization */}
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base font-semibold text-gray-700">Subject Specialization</span>
              </label>
              <div className="space-y-3">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {SPECIALIZATION_OPTIONS.map((option) => (
                    <button
                      key={option}
                      type="button"
                      onClick={() => setValue('specialization', option)}
                      className={cn(
                        'p-3 text-sm rounded-lg border-2 transition-all duration-300 text-left font-medium',
                        watchedSpecialization === option
                          ? 'border-primary bg-primary/10 text-primary ring-2 ring-primary/20'
                          : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                      )}
                    >
                      {option}
                    </button>
                  ))}
                </div>
                <input
                  type="text"
                  placeholder="Or enter your own specialization..."
                  className={cn(
                    'input input-lg input-bordered w-full text-base',
                    errors.specialization
                      ? 'input-error'
                      : 'focus:input-primary'
                  )}
                  {...register('specialization')}
                />
              </div>
              {errors.specialization && (
                <label className="label">
                  <span className="label-text-alt text-error font-medium">
                    {errors.specialization.message}
                  </span>
                </label>
              )}
            </div>

            {/* Experience */}
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base font-semibold text-gray-700">Teaching Experience</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {EXPERIENCE_OPTIONS.map((option) => (
                  <button
                    key={option}
                    type="button"
                    onClick={() => setValue('experience', option)}
                    className={cn(
                      'p-3 text-sm rounded-lg border-2 transition-all duration-300 text-left flex items-center gap-3 font-medium',
                      watchedExperience === option
                        ? 'border-primary bg-primary/10 text-primary ring-2 ring-primary/20'
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                    )}
                  >
                    <Icon variant="star" size={4} className="text-primary" />
                    <span>{option}</span>
                  </button>
                ))}
              </div>
              {errors.experience && (
                <label className="label">
                  <span className="label-text-alt text-error font-medium">
                    {errors.experience.message}
                  </span>
                </label>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleBack}
                disabled={isLoading}
                className="btn btn-outline btn-lg flex items-center gap-2"
              >
                <Icon variant="arrow-left" size={4} />
                <span>Back</span>
              </button>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleSkip}
                  disabled={isLoading}
                  className="btn btn-ghost btn-lg text-gray-600 hover:text-gray-800"
                >
                  Skip
                </button>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary btn-lg flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 shadow-lg"
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <span>Complete Profile</span>
                      <Icon variant="arrow-right" size={4} />
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </motion.div>
  );
}
