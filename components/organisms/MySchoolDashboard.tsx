'use client';

import React, { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  School,
  Edit,
  Users,
  GraduationCap,
  UserCheck,
  ExternalLink,
  Settings,
  Eye,
  ChevronDown,
  MapPin,
  Phone,
  Mail,
  Building2,
  Plus
} from 'lucide-react';
import { getMySchool, updateMySchool } from '@/actions/school.action';
import { ISchoolResponse } from '@/apis/schoolApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { updateSchoolFormSchema, UpdateSchoolFormData } from '@/lib/validators/school.validator';
import { IntegratedSchoolSetup } from '@/components/organisms/IntegratedSchoolSetup/IntegratedSchoolSetup';

export const MySchoolDashboard: React.FC = () => {
  const [schoolData, setSchoolData] = useState<ISchoolResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateMessage, setUpdateMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm<UpdateSchoolFormData>({
    resolver: zodResolver(updateSchoolFormSchema),
  });

  // Fetch school data on component mount
  useEffect(() => {
    const fetchSchoolData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response: TTransformResponse<ISchoolResponse | null> = await getMySchool();
        
        if (response.status === 'success') {
          setSchoolData(response.data);
        } else {
          const errorMsg = Array.isArray(response.message)
            ? response.message.map((m: any) =>
                typeof m === 'object' && m.field && m.constraints
                  ? `${m.field}: ${m.constraints}`
                  : String(m)
              ).join(', ')
            : String(response.message || 'Failed to fetch school data');
          setError(errorMsg);
        }
      } catch (err: any) {
        console.error('Error fetching school data:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchoolData();
  }, []);

  // Handle edit button click
  const handleEditClick = () => {
    if (schoolData) {
      // Pre-fill form with current school data
      setValue('name', schoolData.name);
      setValue('address', schoolData.address);
      setValue('phoneNumber', schoolData.phoneNumber);
      setValue('email', schoolData.email);
      setIsEditModalOpen(true);
      setUpdateMessage(null);
    }
  };

  // Handle form submission for editing
  const onSubmitEdit: SubmitHandler<UpdateSchoolFormData> = async (data) => {
    setIsUpdating(true);
    setUpdateMessage(null);

    try {
      // Create FormData for the updateMySchool action
      const formData = new FormData();
      if (data.name) formData.append('name', data.name);
      if (data.address) formData.append('address', data.address);
      if (data.phoneNumber) formData.append('phoneNumber', data.phoneNumber);
      if (data.email) formData.append('email', data.email);
      if (data.registeredNumber) formData.append('registeredNumber', data.registeredNumber);

      const result = await updateMySchool(formData);

      if (result.status === 'success') {
        setSchoolData(result.data);
        setUpdateMessage({ type: 'success', text: 'School updated successfully!' });
        setTimeout(() => {
          setIsEditModalOpen(false);
          setUpdateMessage(null);
        }, 2000);
      } else {
        const errorMsg = Array.isArray(result.message)
          ? result.message.map((m: any) => 
              typeof m === 'object' && m.message ? m.message : String(m)
            ).join(', ')
          : String(result.message || 'Failed to update school');
        setUpdateMessage({ type: 'error', text: errorMsg });
      }
    } catch (error: any) {
      console.error('Error updating school:', error);
      setUpdateMessage({ type: 'error', text: error.message || 'An unexpected error occurred' });
    } finally {
      setIsUpdating(false);
    }
  };

  // Get status badge color based on school status (placeholder logic)
  const getStatusBadgeClass = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'published':
        return 'badge badge-success';
      case 'draft':
        return 'badge badge-warning';
      case 'archived':
        return 'badge badge-error';
      default:
        return 'badge badge-primary';
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* School Info Card Skeleton */}
          <div className="lg:col-span-2">
            <div className="card bg-base-100 shadow-md">
              <div className="card-body">
                <div className="skeleton h-6 w-3/4 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-5/6"></div>
                  <div className="skeleton h-4 w-4/6"></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Stats Cards Skeleton */}
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="card bg-base-100 shadow-md">
                <div className="card-body">
                  <div className="skeleton h-4 w-1/2 mb-2"></div>
                  <div className="skeleton h-8 w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </div>
      </div>
    );
  }

  // Empty state - no school data, show integrated setup
  if (!schoolData) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <IntegratedSchoolSetup
          onSuccess={() => {
            // Refresh school data after successful creation
            window.location.reload();
          }}
          onError={(error) => {
            console.error('School creation error:', error);
          }}
        />
      </div>
    );
  }

  // Main dashboard with school data
  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* School Information Card - Takes 2/3 of the grid */}
        <div className="lg:col-span-2">
          <div className="card bg-base-100 shadow-md">
            <div className="card-body">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <Building2 className="h-8 w-8 text-primary" />
                  <div>
                    <h2 className="card-title text-2xl">{schoolData.name}</h2>
                    <div className={getStatusBadgeClass('published')}>
                      Published
                    </div>
                  </div>
                </div>
                <div className="card-actions">
                  <button 
                    className="btn btn-outline"
                    onClick={handleEditClick}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit School Details
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                {/* Address */}
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-700">Address</p>
                    <p className="text-gray-600">{schoolData.address}</p>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-start gap-3">
                  <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-700">Phone</p>
                    <p className="text-gray-600">{schoolData.phoneNumber}</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-700">Email</p>
                    <p className="text-gray-600">{schoolData.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics and Quick Actions - Takes 1/3 of the grid */}
        <div className="space-y-6">
          {/* School Statistics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">School Statistics</h3>
            
            {/* Students Count */}
            <div className="card bg-base-100 shadow-md">
              <div className="card-body">
                <div className="flex items-center gap-3">
                  <Users className="h-6 w-6 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Students</p>
                    <p className="text-2xl font-bold text-gray-800">245</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Active Courses */}
            <div className="card bg-base-100 shadow-md">
              <div className="card-body">
                <div className="flex items-center gap-3">
                  <GraduationCap className="h-6 w-6 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Active Courses</p>
                    <p className="text-2xl font-bold text-gray-800">12</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Staff Members */}
            <div className="card bg-base-100 shadow-md">
              <div className="card-body">
                <div className="flex items-center gap-3">
                  <UserCheck className="h-6 w-6 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Staff Members</p>
                    <p className="text-2xl font-bold text-gray-800">18</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions Menu */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            <div className="dropdown dropdown-end w-full">
              <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                <span>More Actions</span>
                <ChevronDown className="h-4 w-4" />
              </div>
              <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-md">
                <li>
                  <a className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    View Public Profile
                  </a>
                </li>
                <li>
                  <a className="flex items-center gap-2">
                    <GraduationCap className="h-4 w-4" />
                    Manage Courses
                  </a>
                </li>
                <li>
                  <a className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Invite Staff
                  </a>
                </li>
                <li>
                  <a className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    School Settings
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box w-11/12 max-w-2xl">
            <h3 className="font-bold text-lg mb-4">Edit School Details</h3>
            
            {/* Update Message */}
            {updateMessage && (
              <div className={`alert ${updateMessage.type === 'success' ? 'alert-success' : 'alert-error'} mb-4`}>
                <span>{updateMessage.text}</span>
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmitEdit)} className="space-y-4">
              {/* School Name Field */}
              <div className="form-control">
                <label htmlFor="edit-name" className="label">
                  <span className="label-text font-medium">School Name *</span>
                </label>
                <input
                  id="edit-name"
                  type="text"
                  placeholder="Enter school name"
                  className={`input input-bordered w-full ${errors.name ? 'input-error' : ''}`}
                  {...register('name')}
                  disabled={isUpdating}
                />
                {errors.name && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.name.message}</span>
                  </div>
                )}
              </div>

              {/* School Address Field */}
              <div className="form-control">
                <label htmlFor="edit-address" className="label">
                  <span className="label-text font-medium">School Address</span>
                </label>
                <input
                  id="edit-address"
                  type="text"
                  placeholder="Enter school address (optional)"
                  className={`input input-bordered w-full ${errors.address ? 'input-error' : ''}`}
                  {...register('address')}
                  disabled={isUpdating}
                />
                {errors.address && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.address.message}</span>
                  </div>
                )}
              </div>

              {/* School Phone Field */}
              <div className="form-control">
                <label htmlFor="edit-phoneNumber" className="label">
                  <span className="label-text font-medium">School Phone</span>
                </label>
                <input
                  id="edit-phoneNumber"
                  type="tel"
                  placeholder="Enter school phone number (optional)"
                  className={`input input-bordered w-full ${errors.phoneNumber ? 'input-error' : ''}`}
                  {...register('phoneNumber')}
                  disabled={isUpdating}
                />
                {errors.phoneNumber && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.phoneNumber.message}</span>
                  </div>
                )}
              </div>

              {/* School Email Field */}
              <div className="form-control">
                <label htmlFor="edit-email" className="label">
                  <span className="label-text font-medium">School Email</span>
                </label>
                <input
                  id="edit-email"
                  type="email"
                  placeholder="Enter school email address (optional)"
                  className={`input input-bordered w-full ${errors.email ? 'input-error' : ''}`}
                  {...register('email')}
                  disabled={isUpdating}
                />
                {errors.email && (
                  <div className="label">
                    <span className="label-text-alt text-error">{errors.email.message}</span>
                  </div>
                )}
              </div>

              <div className="modal-action">
                <button
                  type="button"
                  className="btn btn-ghost"
                  onClick={() => {
                    setIsEditModalOpen(false);
                    setUpdateMessage(null);
                    reset();
                  }}
                  disabled={isUpdating}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={`btn btn-primary ${isUpdating ? 'loading' : ''}`}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Updating...
                    </>
                  ) : (
                    'Update School'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MySchoolDashboard;
