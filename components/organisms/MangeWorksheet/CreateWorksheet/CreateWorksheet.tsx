import { getOptionsTypes } from '@/apis/optionsApi';
import {
  ManageWorksheetView,
  TConfig,
} from '@/components/molecules/ManageWorksheet/ManageWorksheetView/ManageWorksheetView';

export const CreateWorksheet: React.FC = async ({}) => {
  const res = await getOptionsTypes();

  if (res.status === 'error') return <div>Something wrong !</div>;

  // Create the initial config from API data
  const initialConfig: TConfig = res.data.map((q) => ({
    stepLabel: q?.label,
    formSetting: {
      name: q?.key,
      type: q.key === 'question_type' ? 'questionTypes' : 'question',
      question: q.description,
      options: q?.values?.map((v) => ({
        value: v?.id,
        label: v?.label,
        isActive: v?.isActive,
      })),
    },
  }));

  // Find the index of the Subject step
  const subjectIndex = initialConfig.findIndex(item => item.formSetting.name === 'subject');

  // If subject step is not found, try to find it by step<PERSON>abel instead
  let effectiveSubjectIndex = subjectIndex;
  if (subjectIndex === -1) {
    effectiveSubjectIndex = initialConfig.findIndex(item => item.stepLabel === 'Subject');
  }

  // Create the Topic step
  const topicStep = {
    stepLabel: 'Topic',
    formSetting: {
      name: 'subject',
      type: 'topicSelector',
      question: 'Select topics for your worksheet',
    },
  };

  // Insert the Topic step after the Subject step and before the Level step
  // This ensures the workflow follows: Grade -> Subject -> Topic -> Level -> Language -> Question Count -> Question Type

  // Create a new config array and manually add each item in the correct order
  const config: TConfig = [];

  // If we still can't find the Subject step, use a default position (after the first step)
  if (effectiveSubjectIndex === -1) {
    // Add the first step (Grade)
    if (initialConfig.length > 0) {
      config.push(initialConfig[0]);
    }
    // Add the Topic step after the first step
    config.push(topicStep);
    // Add the remaining items
    for (let i = 1; i < initialConfig.length; i++) {
      config.push(initialConfig[i]);
    }
  } else {
    // Add items before and including the Subject step
    for (let i = 0; i <= effectiveSubjectIndex; i++) {
      config.push(initialConfig[i]);
    }

    // Add the Topic step after the Subject step
    config.push(topicStep);

    // Add the remaining items after the Subject step
    for (let i = effectiveSubjectIndex + 1; i < initialConfig.length; i++) {
      config.push(initialConfig[i]);
    }
  }


  // Add a key to force re-render when config changes

  // --- BEGIN MODIFICATION for Custom Question Count ---
  // Find the question_count step in the config
  const questionCountStepIndex = config.findIndex(
    (item) => item.formSetting.name === 'question_count'
  );

  if (questionCountStepIndex !== -1) {
    // Add the "Custom" option to its options list
    const existingOptions = config[questionCountStepIndex].formSetting.options || [];
    config[questionCountStepIndex].formSetting.options = [
      ...existingOptions,
      { label: 'Custom', value: 'custom', isActive: true }, // Value 'custom' should match RHFQuestion logic
    ];

    // Add a flag to enable custom input functionality for this specific question
    // This assumes TFormSetting in TConfig can hold this property.
    // If not, ManageWorksheetView.tsx might need type updates.
    (config[questionCountStepIndex].formSetting as any).enableCustomInput = true; 
  }
  // --- END MODIFICATION ---

  return <ManageWorksheetView key="create-worksheet-view" config={config} />;
};
