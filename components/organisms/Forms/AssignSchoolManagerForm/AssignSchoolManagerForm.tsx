'use client';

import React, { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/atoms/Button/Button';
import { Form } from '@/components/atoms/Form/Form';
import { Label } from '@/components/atoms/Label/Label';
import { EUserRole } from '@/config/enums/user';
import { handleAssignSchoolManagerAction } from '@/actions/school.action'; // Import server action
// Mock data and types will be replaced or fetched via API actions later if needed for dropdowns
// For now, focusing on form submission with server action

interface IAssignSchoolManagerFormInput {
  schoolId: string;
  schoolManagerId: string;
}

// Placeholder types - replace with actual types
interface ISchool {
  id: string;
  name: string;
}
interface IUser {
  id: string;
  name: string;
  role: EUserRole;
}


const assignSchoolManagerSchema = z.object({
  schoolId: z.string().uuid({ message: 'Please select a school' }),
  schoolManagerId: z.string().uuid({ message: 'Please select a school manager' }),
});

// Mock data - replace with actual API calls
const mockSchools: ISchool[] = [
  { id: '123e4567-e89b-12d3-a456-************', name: 'Springfield Elementary' },
  { id: '987e6543-e21b-12d3-a456-************', name: 'Shelbyville Middle School' },
];

const mockUsers: IUser[] = [
  { id: 'abcde123-e89b-12d3-a456-************', name: 'Seymour Skinner', role: EUserRole.SCHOOL_MANAGER },
  { id: 'fghij456-e89b-12d3-a456-************', name: 'Edna Krabappel', role: EUserRole.TEACHER },
  { id: 'klmno789-e89b-12d3-a456-************', name: 'Principal Valiant', role: EUserRole.SCHOOL_MANAGER },
];


export const AssignSchoolManagerForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [schools, setSchools] = useState<ISchool[]>([]);
  const [schoolManagers, setSchoolManagers] = useState<IUser[]>([]);

  const methods = useForm<IAssignSchoolManagerFormInput>({
    resolver: zodResolver(assignSchoolManagerSchema),
  });

  const { register, handleSubmit, formState: { errors }, reset } = methods;

  useEffect(() => {
    // In a real app, fetch schools and users (filtered for school_manager role)
    // For now, using mock data. This part would ideally use server actions too if data is dynamic.
    setSchools(mockSchools);
    setSchoolManagers(mockUsers.filter(user => user.role === EUserRole.SCHOOL_MANAGER));
  }, []);

  const onSubmitHandler: SubmitHandler<IAssignSchoolManagerFormInput> = async (formData) => {
    setIsLoading(true);
    setError(null);
    try {
      const { schoolId, schoolManagerId } = formData;
      const result = await handleAssignSchoolManagerAction(schoolId, { adminId: schoolManagerId });

      if (result.status === 'success') {
        alert('School manager assigned successfully!');
        // reset(); // Optionally reset the form
      } else {
        const errorMessage = Array.isArray(result.message)
          ? result.message.map((m: any) => `${m.field}: ${m.constraints}`).join(', ')
          : result.message;
        setError(errorMessage as string || 'Failed to assign school manager.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...methods}>
      <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-4">
        <div>
          <Label htmlFor="schoolId">Select School</Label>
          <select
            id="schoolId"
            {...register('schoolId')}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">-- Select a School --</option>
            {schools.map((school) => (
              <option key={school.id} value={school.id}>
                {school.name}
              </option>
            ))}
          </select>
          {errors.schoolId && <p className="text-red-500 text-sm">{errors.schoolId.message}</p>}
        </div>

        <div>
          <Label htmlFor="schoolManagerId">Select School Manager</Label>
          <select
            id="schoolManagerId"
            {...register('schoolManagerId')}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">-- Select a School Manager --</option>
            {schoolManagers.map((manager) => (
              <option key={manager.id} value={manager.id}>
                {manager.name} ({manager.role})
              </option>
            ))}
          </select>
          {errors.schoolManagerId && <p className="text-red-500 text-sm">{errors.schoolManagerId.message}</p>}
        </div>

        {error && <p className="text-red-500 text-sm">{error}</p>}

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Assigning...' : 'Assign School Manager'}
        </Button>
      </form>
    </Form>
  );
};
