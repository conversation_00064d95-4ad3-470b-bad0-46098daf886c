'use client';

import React from 'react';
import Link from 'next/link';
import { UserCircle, Mail, School, Shield, Edit, Calendar, Key } from 'lucide-react';
import { InfoField } from '@/components/molecules/InfoField/InfoField';
import { EUserRole } from '@/config/enums/user';

export interface UserDetailsCardProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: EUserRole;
    schoolId?: string | null;
  };
}

export const UserDetailsCard: React.FC<UserDetailsCardProps> = ({ user }) => {
  // Format the role for display
  const formattedRole = 
    user.role === EUserRole.ADMIN 
      ? 'Admin' 
      : user.role === EUserRole.TEACHER 
        ? 'Teacher' 
        : user.role === EUserRole.SCHOOL_MANAGER 
          ? 'School Manager' 
          : user.role === EUserRole.STUDENT 
            ? 'Student' 
            : user.role;

  // Get role badge styling
  const getRoleBadgeClass = () => {
    switch (user.role) {
      case EUserRole.ADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case EUserRole.TEACHER:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case EUserRole.SCHOOL_MANAGER:
        return 'bg-green-100 text-green-800 border-green-200';
      case EUserRole.STUDENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get avatar gradient based on role
  const getAvatarGradient = () => {
    switch (user.role) {
      case EUserRole.ADMIN:
        return 'from-purple-500 to-indigo-600';
      case EUserRole.TEACHER:
        return 'from-blue-500 to-cyan-600';
      case EUserRole.SCHOOL_MANAGER:
        return 'from-green-500 to-emerald-600';
      case EUserRole.STUDENT:
        return 'from-yellow-500 to-amber-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  // Get role icon
  const getRoleIcon = () => {
    switch (user.role) {
      case EUserRole.ADMIN:
        return <Shield size={20} className="text-purple-500" />;
      case EUserRole.TEACHER:
        return <UserCircle size={20} className="text-blue-500" />;
      case EUserRole.SCHOOL_MANAGER:
        return <School size={20} className="text-green-500" />;
      case EUserRole.STUDENT:
        return <Calendar size={20} className="text-yellow-500" />;
      default:
        return <UserCircle size={20} className="text-gray-500" />;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl">
      {/* Header with gradient background based on role */}
      <div className={`bg-gradient-to-r ${getAvatarGradient()} h-24 relative`}>
        <div className="absolute -bottom-12 left-6 w-24 h-24 rounded-full flex items-center justify-center bg-white p-1 shadow-lg">
          <div className={`w-full h-full rounded-full flex items-center justify-center bg-gradient-to-r ${getAvatarGradient()} text-white`}>
            <UserCircle size={56} />
          </div>
        </div>
      </div>

      <div className="p-6 pt-16">
        {/* User info */}
        <div className="flex flex-col sm:flex-row sm:items-end gap-4 mb-8">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-800">{user.name}</h2>
            <div className="flex items-center flex-wrap gap-2 mt-2">
              <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium border ${getRoleBadgeClass()}`}>
                {getRoleIcon()}
                <span className="ml-1.5">{formattedRole}</span>
              </span>
              <span className="text-gray-500 text-sm flex items-center">
                <Calendar size={16} className="mr-1.5" />
                User since {new Date().toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* User details in a card grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg p-5 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-blue-200 group">
            <InfoField 
              label="Email Address" 
              value={user.email} 
              icon={<Mail size={20} className="text-blue-500 group-hover:scale-110 transition-transform duration-300" />} 
            />
          </div>

          <div className="bg-white rounded-lg p-5 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-green-200 group">
            <InfoField 
              label="School Assignment" 
              value={user.schoolId || 'Not assigned to any school'} 
              icon={<School size={20} className="text-green-500 group-hover:scale-110 transition-transform duration-300" />} 
            />
          </div>

          <div className="bg-white rounded-lg p-5 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-purple-200 group">
            <InfoField 
              label="System Role" 
              value={formattedRole} 
              icon={getRoleIcon()}
            />
          </div>

          <div className="bg-white rounded-lg p-5 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 hover:border-gray-300 group">
            <InfoField 
              label="User ID" 
              value={user.id} 
              icon={<Key size={20} className="text-gray-500 group-hover:scale-110 transition-transform duration-300" />} 
            />
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex justify-end mt-8 pt-6 border-t border-gray-100">
          <Link 
            href={`/app/users-management/edit/${user.id}`}
            className="inline-flex items-center gap-2 px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
          >
            <Edit size={18} />
            Edit User
          </Link>
        </div>
      </div>
    </div>
  );
};
