'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Building2, 
  Palette, 
  Save, 
  Loader2,
  Check<PERSON>ircle,
  AlertCircle,
  Upload,
  Settings
} from 'lucide-react';
import { getMySchool, updateMySchool } from '@/actions/school.action';
import { handleCreateBrandAction, handleUpdateBrandAction } from '@/actions/brand.action';
import { handleFileUploadAction } from '@/actions/file.action';
import { ISchoolResponse } from '@/apis/schoolApi';
import { IBrandResponse } from '@/apis/brandApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { FileUpload } from '@/components/molecules/FileUpload/FileUpload';
import { convertToRenderUrl } from '@/utils/fileUtils';
import { cn } from '@/utils/cn';

// Validation schemas
const schoolDetailsSchema = z.object({
  name: z.string().min(1, 'School name is required').max(255, 'School name is too long'),
  address: z.string().optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
});

const brandingSchema = z.object({
  primaryColor: z.string().regex(/^#([0-9A-Fa-f]{3}){1,2}$/, 'Invalid color format').optional().or(z.literal('')),
  secondaryColor: z.string().regex(/^#([0-9A-Fa-f]{3}){1,2}$/, 'Invalid color format').optional().or(z.literal('')),
  motto: z.string().max(255, 'Motto is too long').optional(),
});

type SchoolDetailsFormData = z.infer<typeof schoolDetailsSchema>;
type BrandingFormData = z.infer<typeof brandingSchema>;

interface SchoolCustomizationState {
  schoolData: ISchoolResponse | null;
  isLoading: boolean;
  error: string | null;
  schoolDetailsLoading: boolean;
  brandingLoading: boolean;
  successMessage: string | null;
}

export const SchoolCustomization: React.FC = () => {
  // State management
  const [state, setState] = useState<SchoolCustomizationState>({
    schoolData: null,
    isLoading: true,
    error: null,
    schoolDetailsLoading: false,
    brandingLoading: false,
    successMessage: null,
  });

  // File upload states
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoUploading, setLogoUploading] = useState(false);
  
  const [brandImageFile, setBrandImageFile] = useState<File | null>(null);
  const [brandImagePreview, setBrandImagePreview] = useState<string | null>(null);
  const [brandImageUploading, setBrandImageUploading] = useState(false);

  // Form setup
  const schoolDetailsForm = useForm<SchoolDetailsFormData>({
    resolver: zodResolver(schoolDetailsSchema),
    defaultValues: {
      name: '',
      address: '',
      phoneNumber: '',
      email: '',
    },
  });

  const brandingForm = useForm<BrandingFormData>({
    resolver: zodResolver(brandingSchema),
    defaultValues: {
      primaryColor: '#3B82F6',
      secondaryColor: '#10B981',
      motto: '',
    },
  });

  const fetchSchoolData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const response: TTransformResponse<ISchoolResponse | null> = await getMySchool();
      
      if (response.status === 'success' && response.data) {
        const schoolData = response.data;
        setState(prev => ({ ...prev, schoolData, isLoading: false }));
        
        // Pre-fill school details form
        schoolDetailsForm.reset({
          name: schoolData.name || '',
          address: schoolData.address || '',
          phoneNumber: schoolData.phoneNumber || '',
          email: schoolData.email || '',
        });

        // Pre-fill branding form if brand exists
        if (schoolData.brand) {
          brandingForm.reset({
            primaryColor: schoolData.brand.color || '#3B82F6',
            secondaryColor: '#10B981', // Default secondary color
            motto: '', // Add motto field to brand if needed
          });

          // Set logo preview if exists
          if (schoolData.brand.logo) {
            setLogoPreview(convertToRenderUrl(schoolData.brand.logo));
          }

          // Set brand image preview if exists
          if (schoolData.brand.image) {
            setBrandImagePreview(convertToRenderUrl(schoolData.brand.image));
          }
        }
      } else {
        const errorMsg = 'Failed to fetch school data';
        setState(prev => ({ ...prev, error: errorMsg, isLoading: false }));
      }
    } catch (err: any) {
      console.error('Error fetching school data:', err);
      setState(prev => ({ 
        ...prev, 
        error: err.message || 'An unexpected error occurred', 
        isLoading: false
      }));
    }
  }, [brandingForm, schoolDetailsForm]);

  // Fetch school data on component mount
  useEffect(() => {
    fetchSchoolData();
  }, [fetchSchoolData]);

  // Handle school details form submission
  const handleSchoolDetailsSubmit = async (data: SchoolDetailsFormData) => {
    try {
      setState(prev => ({ ...prev, schoolDetailsLoading: true, error: null, successMessage: null }));

      // Create FormData for the API call
      const formData = new FormData();
      formData.append('name', data.name);
      if (data.address) formData.append('address', data.address);
      if (data.phoneNumber) formData.append('phoneNumber', data.phoneNumber);
      if (data.email) formData.append('email', data.email);

      const response = await updateMySchool(formData);

      if (response.status === 'success') {
        setState(prev => ({
          ...prev,
          schoolDetailsLoading: false,
          successMessage: 'School details updated successfully!',
          schoolData: response.data || prev.schoolData
        }));

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setState(prev => ({ ...prev, successMessage: null }));
        }, 5000);
      } else {
        const errorMsg = Array.isArray(response.message)
          ? response.message.join(', ')
          : String(response.message || 'Failed to update school details');
        setState(prev => ({
          ...prev,
          schoolDetailsLoading: false,
          error: errorMsg
        }));
      }
    } catch (err: any) {
      console.error('Error updating school details:', err);
      setState(prev => ({
        ...prev,
        schoolDetailsLoading: false,
        error: err.message || 'An unexpected error occurred'
      }));
    }
  };

  // Handle logo upload
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLogoUploading(true);
      const response = await handleFileUploadAction(file, 'School logo', 'branding');

      if (response.status === 'success' && response.data) {
        setLogoFile(file);
        setLogoPreview(response.data.url);
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to upload logo. Please try again.'
        }));
      }
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to upload logo'
      }));
    } finally {
      setLogoUploading(false);
    }
  };

  // Handle logo clear
  const handleClearLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
  };

  // Handle brand image upload
  const handleBrandImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setBrandImageUploading(true);
      const response = await handleFileUploadAction(file, 'Brand image', 'branding');

      if (response.status === 'success' && response.data) {
        setBrandImageFile(file);
        setBrandImagePreview(response.data.url);
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to upload brand image. Please try again.'
        }));
      }
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        error: err.message || 'Failed to upload brand image'
      }));
    } finally {
      setBrandImageUploading(false);
    }
  };

  // Handle brand image clear
  const handleClearBrandImage = () => {
    setBrandImageFile(null);
    setBrandImagePreview(null);
  };

  // Handle branding form submission
  const handleBrandingSubmit = async (data: BrandingFormData) => {
    try {
      setState(prev => ({ ...prev, brandingLoading: true, error: null, successMessage: null }));

      // Prepare brand payload
      const brandPayload = {
        logo: logoPreview || undefined,
        color: data.primaryColor || undefined,
        image: brandImagePreview || undefined,
      };

      let brandResponse;
      const existingBrand = state.schoolData?.brand;

      if (existingBrand) {
        // Update existing brand
        brandResponse = await handleUpdateBrandAction(existingBrand.id, brandPayload);
      } else {
        // Create new brand
        brandResponse = await handleCreateBrandAction(brandPayload);
      }

      if (brandResponse.status === 'success' && brandResponse.data) {
        // If creating new brand, associate it with the school
        if (!existingBrand) {
          const schoolFormData = new FormData();
          schoolFormData.append('brandId', brandResponse.data.id);

          const schoolUpdateResponse = await updateMySchool(schoolFormData);
          if (schoolUpdateResponse.status !== 'success') {
            throw new Error('Failed to associate brand with school');
          }
        }

        setState(prev => ({
          ...prev,
          brandingLoading: false,
          successMessage: existingBrand ? 'Branding updated successfully!' : 'Branding created successfully!',
        }));

        // Refresh school data to get updated brand info
        await fetchSchoolData();

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setState(prev => ({ ...prev, successMessage: null }));
        }, 5000);
      } else {
        const errorMsg = 'Failed to save branding';
        setState(prev => ({
          ...prev,
          brandingLoading: false,
          error: errorMsg
        }));
      }
    } catch (err: any) {
      console.error('Error saving branding:', err);
      setState(prev => ({
        ...prev,
        brandingLoading: false,
        error: err.message || 'An unexpected error occurred'
      }));
    }
  };

  // Show loading state
  if (state.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <div className="loading loading-spinner loading-lg"></div>
          <p className="text-gray-600">Loading school data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (state.error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="alert alert-error max-w-md">
          <AlertCircle className="w-5 h-5" />
          <div>
            <h3 className="font-bold">Error Loading School Data</h3>
            <div className="text-sm">{state.error}</div>
          </div>
          <button 
            className="btn btn-sm btn-outline"
            onClick={fetchSchoolData}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-3">
          <Settings className="w-8 h-8 text-primary" />
          Customize Your School
        </h1>
        <p className="text-gray-600">
          Update your school details and customize your branding
        </p>
      </div>

      {/* Success Message */}
      {state.successMessage && (
        <div className="alert alert-success">
          <CheckCircle className="w-5 h-5" />
          <span>{state.successMessage}</span>
          <button 
            className="btn btn-sm btn-ghost"
            onClick={() => setState(prev => ({ ...prev, successMessage: null }))}
          >
            ✕
          </button>
        </div>
      )}

      {/* School Details Section */}
      <div className="card bg-base-100 shadow-lg">
        <div className="card-body">
          <h2 className="card-title text-xl flex items-center gap-2">
            <Building2 className="w-6 h-6 text-primary" />
            School Details
          </h2>
          <p className="text-gray-600 mb-6">Update your school&apos;s basic information</p>

          <form onSubmit={schoolDetailsForm.handleSubmit(handleSchoolDetailsSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* School Name */}
              <div className="md:col-span-2">
                <label className="label">
                  <span className="label-text font-medium">School Name *</span>
                </label>
                <input
                  type="text"
                  className={cn(
                    "input input-bordered w-full",
                    schoolDetailsForm.formState.errors.name && "input-error"
                  )}
                  placeholder="Enter school name"
                  {...schoolDetailsForm.register('name')}
                />
                {schoolDetailsForm.formState.errors.name && (
                  <div className="label">
                    <span className="label-text-alt text-error">
                      {schoolDetailsForm.formState.errors.name.message}
                    </span>
                  </div>
                )}
              </div>

              {/* Address */}
              <div className="md:col-span-2">
                <label className="label">
                  <span className="label-text font-medium">Address</span>
                </label>
                <textarea
                  className="textarea textarea-bordered w-full"
                  placeholder="Enter school address"
                  rows={3}
                  {...schoolDetailsForm.register('address')}
                />
              </div>

              {/* Phone Number */}
              <div>
                <label className="label">
                  <span className="label-text font-medium">Phone Number</span>
                </label>
                <input
                  type="tel"
                  className="input input-bordered w-full"
                  placeholder="Enter phone number"
                  {...schoolDetailsForm.register('phoneNumber')}
                />
              </div>

              {/* Email */}
              <div>
                <label className="label">
                  <span className="label-text font-medium">Email</span>
                </label>
                <input
                  type="email"
                  className={cn(
                    "input input-bordered w-full",
                    schoolDetailsForm.formState.errors.email && "input-error"
                  )}
                  placeholder="Enter email address"
                  {...schoolDetailsForm.register('email')}
                />
                {schoolDetailsForm.formState.errors.email && (
                  <div className="label">
                    <span className="label-text-alt text-error">
                      {schoolDetailsForm.formState.errors.email.message}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={state.schoolDetailsLoading}
              >
                {state.schoolDetailsLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Update School Details
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Branding Section */}
      <div className="card bg-base-100 shadow-lg">
        <div className="card-body">
          <h2 className="card-title text-xl flex items-center gap-2">
            <Palette className="w-6 h-6 text-primary" />
            School Branding
          </h2>
          <p className="text-gray-600 mb-6">
            {state.schoolData?.brand ? 'Update your school&apos;s branding' : 'Create your school&apos;s branding'}
          </p>

          <form onSubmit={brandingForm.handleSubmit(handleBrandingSubmit)} className="space-y-8">
            {/* Logo Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Upload className="w-5 h-5 text-primary" />
                School Logo
              </h3>
              <p className="text-sm text-gray-600">Upload your school logo (optional)</p>

              <FileUpload
                id="logo-upload"
                accept="image/*"
                preview={logoPreview}
                onUpload={handleLogoUpload}
                onClear={handleClearLogo}
                label="Click to upload logo"
                maxSize="MAX. 2MB"
                disabled={logoUploading || state.brandingLoading}
              />

              {logoUploading && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Uploading logo...</span>
                </div>
              )}
            </div>

            {/* Brand Image Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Upload className="w-5 h-5 text-primary" />
                Brand Image
              </h3>
              <p className="text-sm text-gray-600">Upload a brand image or banner (optional)</p>

              <FileUpload
                id="brand-image-upload"
                accept="image/*"
                preview={brandImagePreview}
                onUpload={handleBrandImageUpload}
                onClear={handleClearBrandImage}
                label="Click to upload brand image"
                maxSize="MAX. 2MB"
                disabled={brandImageUploading || state.brandingLoading}
              />

              {brandImageUploading && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Uploading brand image...</span>
                </div>
              )}
            </div>

            {/* Color Selection Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-800">Brand Colors</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Primary Color */}
                <div className="space-y-3">
                  <label className="label">
                    <span className="label-text font-medium">Primary Color</span>
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      className="input input-bordered w-16 h-12 p-1 cursor-pointer"
                      {...brandingForm.register('primaryColor')}
                      disabled={state.brandingLoading}
                    />
                    <input
                      type="text"
                      className={cn(
                        "input input-bordered flex-1",
                        brandingForm.formState.errors.primaryColor && "input-error"
                      )}
                      placeholder="#3B82F6"
                      {...brandingForm.register('primaryColor')}
                      disabled={state.brandingLoading}
                    />
                  </div>
                  {brandingForm.formState.errors.primaryColor && (
                    <div className="label">
                      <span className="label-text-alt text-error">
                        {brandingForm.formState.errors.primaryColor.message}
                      </span>
                    </div>
                  )}
                </div>

                {/* Secondary Color */}
                <div className="space-y-3">
                  <label className="label">
                    <span className="label-text font-medium">Secondary Color</span>
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      className="input input-bordered w-16 h-12 p-1 cursor-pointer"
                      {...brandingForm.register('secondaryColor')}
                      disabled={state.brandingLoading}
                    />
                    <input
                      type="text"
                      className={cn(
                        "input input-bordered flex-1",
                        brandingForm.formState.errors.secondaryColor && "input-error"
                      )}
                      placeholder="#10B981"
                      {...brandingForm.register('secondaryColor')}
                      disabled={state.brandingLoading}
                    />
                  </div>
                  {brandingForm.formState.errors.secondaryColor && (
                    <div className="label">
                      <span className="label-text-alt text-error">
                        {brandingForm.formState.errors.secondaryColor.message}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* School Motto Section */}
            <div className="space-y-3">
              <label className="label">
                <span className="label-text font-medium">School Motto/Tagline</span>
              </label>
              <input
                type="text"
                className="input input-bordered w-full"
                placeholder="Enter your school's motto or tagline"
                {...brandingForm.register('motto')}
                disabled={state.brandingLoading}
              />
              <div className="label">
                <span className="label-text-alt text-gray-500">
                  Optional: A short phrase that represents your school&apos;s values
                </span>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={state.brandingLoading}
              >
                {state.brandingLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    {state.schoolData?.brand ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    {state.schoolData?.brand ? 'Update Branding' : 'Create Branding'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
