'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSession } from 'next-auth/react';
import { Building2, MapPin, Phone, Mail, FileText, Sparkles, ArrowRight } from 'lucide-react';
import { handleCreateSchoolAction } from '@/actions/school.action';
import { createSchoolFormSchema, CreateSchoolFormData } from '@/lib/validators/school.validator';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

interface IntegratedSchoolSetupProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

export const IntegratedSchoolSetup: React.FC<IntegratedSchoolSetupProps> = ({
  onSuccess,
  onError,
  className
}) => {
  const { update: updateSession } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateSchoolFormData>({
    resolver: zodResolver(createSchoolFormSchema),
  });

  const onSubmit: SubmitHandler<CreateSchoolFormData> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      const result = await handleCreateSchoolAction({
        name: data.name,
        address: data.address || '',
        phoneNumber: data.phoneNumber || '',
        registeredNumber: data.registeredNumber || '',
        email: data.email || '',
      });

      if (result.status === 'success') {
        setSuccessMessage('School created successfully!');
        reset();

        // Update session with the new school information
        if (result.data && updateSession) {
          try {
            await updateSession({
              user: {
                schoolId: result.data.id,
                school: {
                  id: result.data.id,
                  name: result.data.name,
                  address: result.data.address,
                  phoneNumber: result.data.phoneNumber,
                  registeredNumber: result.data.registeredNumber,
                  email: result.data.email,
                  brand: result.data.brand,
                }
              }
            });
            console.log('✅ Session updated with new school:', result.data.id);

            // Small delay to ensure session is propagated
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error('❌ Failed to update session:', error);
          }
        }

        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        const errorMsg = result.message || 'Failed to create school';
        setErrorMessage(errorMsg);
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error) {
      console.error('Error creating school:', error);
      const errorMsg = 'An unexpected error occurred while creating the school';
      setErrorMessage(errorMsg);
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <Building2 className="w-8 h-8 text-primary" />
          </div>
          <Sparkles className="w-6 h-6 text-yellow-500" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Create Your School
        </h2>
        <p className="text-gray-600 max-w-md mx-auto">
          Set up your educational institution to start managing students, teachers, and academic content.
        </p>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="alert alert-success mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{successMessage}</span>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="alert alert-error mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{errorMessage}</span>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* School Name - Required */}
        <div className="form-control">
          <label className="label">
            <span className="label-text font-medium flex items-center gap-2">
              <Building2 className="w-4 h-4 text-primary" />
              School Name *
            </span>
          </label>
          <input
            type="text"
            placeholder="Enter your school name"
            className={cn(
              'input input-bordered w-full',
              errors.name && 'input-error'
            )}
            {...register('name')}
          />
          {errors.name && (
            <label className="label">
              <span className="label-text-alt text-error">{errors.name.message}</span>
            </label>
          )}
        </div>

        {/* Address - Optional */}
        <div className="form-control">
          <label className="label">
            <span className="label-text font-medium flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary" />
              Address
            </span>
          </label>
          <textarea
            placeholder="Enter your school address"
            className={cn(
              'textarea textarea-bordered w-full',
              errors.address && 'textarea-error'
            )}
            rows={3}
            {...register('address')}
          />
          {errors.address && (
            <label className="label">
              <span className="label-text-alt text-error">{errors.address.message}</span>
            </label>
          )}
        </div>

        {/* Phone and Email Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Phone Number - Optional */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium flex items-center gap-2">
                <Phone className="w-4 h-4 text-primary" />
                Phone Number
              </span>
            </label>
            <input
              type="tel"
              placeholder="Enter phone number"
              className={cn(
                'input input-bordered w-full',
                errors.phoneNumber && 'input-error'
              )}
              {...register('phoneNumber')}
            />
            {errors.phoneNumber && (
              <label className="label">
                <span className="label-text-alt text-error">{errors.phoneNumber.message}</span>
              </label>
            )}
          </div>

          {/* Email - Optional */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium flex items-center gap-2">
                <Mail className="w-4 h-4 text-primary" />
                Email
              </span>
            </label>
            <input
              type="email"
              placeholder="Enter school email"
              className={cn(
                'input input-bordered w-full',
                errors.email && 'input-error'
              )}
              {...register('email')}
            />
            {errors.email && (
              <label className="label">
                <span className="label-text-alt text-error">{errors.email.message}</span>
              </label>
            )}
          </div>
        </div>

        {/* Registration Number - Optional */}
        <div className="form-control">
          <label className="label">
            <span className="label-text font-medium flex items-center gap-2">
              <FileText className="w-4 h-4 text-primary" />
              Registration Number
            </span>
          </label>
          <input
            type="text"
            placeholder="Enter registration number (if applicable)"
            className={cn(
              'input input-bordered w-full',
              errors.registeredNumber && 'input-error'
            )}
            {...register('registeredNumber')}
          />
          {errors.registeredNumber && (
            <label className="label">
              <span className="label-text-alt text-error">{errors.registeredNumber.message}</span>
            </label>
          )}
        </div>

        {/* Submit Button */}
        <div className="form-control pt-4">
          <Button
            type="submit"
            variant="primary"
            className="w-full h-12 text-base font-semibold"
            isLoading={isSubmitting}
            disabled={isSubmitting}
            iconProps={{
              icon: ArrowRight,
              position: 'right'
            }}
          >
            {isSubmitting ? 'Creating School...' : 'Create School'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default IntegratedSchoolSetup;
