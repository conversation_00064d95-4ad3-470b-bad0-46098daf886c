'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ColumnDef, PaginationState, SortingState, Row, getCoreRowModel, getPaginationRowModel, getSortedRowModel, getFilteredRowModel, useReactTable } from '@tanstack/react-table';
import {
  ChevronDown,
  ChevronUp,
  Edit,
  Eye,
  Loader2,
  School as SchoolIcon,
  Trash2,
  Users,
  AlertCircle,
  Filter as FilterIcon,
  X as XIcon,
} from 'lucide-react';

import { AlertMessage, AlertMessageProps } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import CustomTable from '@/components/molecules/CustomTable/CustomTable';
import { TablePagination } from '@/components/molecules/TablePagination/TablePagination';
import { SchoolTableHeader } from '@/components/molecules/SchoolTableHeader/SchoolTableHeader';
import { SchoolTableFilterPanel, SchoolFilters } from '@/components/molecules/SchoolTableFilterPanel/SchoolTableFilterPanel';
import { SchoolTableBulkActions } from '@/components/molecules/SchoolTableBulkActions/SchoolTableBulkActions';
import { DeleteSchoolModal } from '@/components/organisms/DeleteSchoolModal/DeleteSchoolModal';

import { handleDeleteSchoolAction } from '@/actions/school.action';
import { ISchoolResponse } from '@/apis/schoolApi';
import { AnimationStyles } from '@/components/atoms/AnimationStyles/AnimationStyles';



// Helper to handle message formatting
const formatApiMessage = (message: string | any[] | undefined): string => {
  if (Array.isArray(message)) {
    return message.map(m => typeof m === 'object' && m.message ? m.message : String(m)).join(', ');
  }
  return String(message || '');
};

// Helper for indeterminate checkbox
const IndeterminateCheckbox = React.forwardRef<
  HTMLInputElement,
  { indeterminate?: boolean } & React.HTMLProps<HTMLInputElement>
>(({ indeterminate, className = '', ...rest }, ref) => {
  const defaultRef = useRef<HTMLInputElement>(null);
  const resolvedRef = ref || defaultRef;

  useEffect(() => {
    if (typeof resolvedRef === 'object' && resolvedRef.current) {
      resolvedRef.current.indeterminate = indeterminate ?? false;
    }
  }, [resolvedRef, indeterminate]);

  return (
    <input
      type="checkbox"
      ref={resolvedRef}
      className={`h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 ${className}`}
      {...rest}
    />
  );
});
IndeterminateCheckbox.displayName = 'IndeterminateCheckbox';

export interface SchoolsTableProps {
  schools: ISchoolResponse[];
  isLoading: boolean;
  apiMessage: { type: 'success' | 'error'; message: string } | null;
  setApiMessage: (message: { type: 'success' | 'error'; message: string } | null) => void;
  onRefresh: () => void; // Callback to refresh data from the parent
  onEditSchool: (school: ISchoolResponse) => void; // Callback to handle editing a school
  tableTitle?: string;
  entityName?: string;
  entityNamePlural?: string;
  createPath?: string;
}

export const SchoolsTable: React.FC<SchoolsTableProps> = ({
  schools,
  isLoading: initialLoading,
  apiMessage,
  setApiMessage,
  onRefresh,
  onEditSchool, // Added onEditSchool to destructuring
  tableTitle = "Schools Management",
  entityName = "school",
  entityNamePlural = "schools",
  createPath = "/school-management/create", // This might also need to open a modal
}) => {
  const router = useRouter();

  const [internalLoading, setInternalLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [globalFilter, setGlobalFilter] = useState(''); // For TanStack Table global filter
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  // const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false); // Filter panel UI removed
  // const [filters, setFilters] = useState<SchoolFilters>({}); // Filter state removed

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedSchoolForModal, setSelectedSchoolForModal] = useState<ISchoolResponse | null>(null);

  const isLoading = initialLoading || internalLoading;

  useEffect(() => {
    // Apply search term to global filter for TanStack Table
    setGlobalFilter(searchTerm);
  }, [searchTerm]);
  
  // Apply local filters to data before passing to TanStack table
  const locallyFilteredSchools = useMemo(() => {
    const processedSchools = [...schools];

    return processedSchools;
  }, [schools]); // 'filters' removed from dependencies


  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    // TanStack table's global filter will handle this. Pagination reset is handled by table state.
  };

  const clearSearch = () => {
    setSearchTerm('');
  };


  const handleViewSchool = (schoolId: string) => {
    router.push(`/school-management/${schoolId}`); // Corrected view path
  };

  // handleEditSchool will now call the onEditSchool prop
  // const handleEditSchool = (schoolId: string) => {
  //   router.push(`/school-management/edit/${schoolId}`); 
  // };

  const handleOpenDeleteModal = (school: ISchoolResponse) => {
    setSelectedSchoolForModal(school);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setSelectedSchoolForModal(null);
  };

  const handleDeleteSchool = async () => {
    if (!selectedSchoolForModal) return;
    setInternalLoading(true);
    setApiMessage(null);
    try {
      const response = await handleDeleteSchoolAction(selectedSchoolForModal.id);
      if (response.status === 'success') {
        setApiMessage({ type: 'success', message: `${entityName.charAt(0).toUpperCase() + entityName.slice(1)} deleted successfully.` });
        onRefresh();
      } else {
        setApiMessage({ type: 'error', message: formatApiMessage(response.message) || `Failed to delete ${entityName}.` });
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: formatApiMessage(error.message) || `An error occurred during deletion.` });
    } finally {
      setInternalLoading(false);
      handleCloseDeleteModal();
    }
  };

  const selectedSchoolIds = useMemo(() => {
    return Object.keys(rowSelection).filter(key => rowSelection[key as keyof typeof rowSelection]);
  }, [rowSelection]);

  const handleBulkDelete = async () => {
    if (selectedSchoolIds.length === 0) return;
    if (!confirm(`Are you sure you want to delete ${selectedSchoolIds.length} selected ${selectedSchoolIds.length === 1 ? entityName : entityNamePlural}?`)) {
      return;
    }
    setInternalLoading(true);
    setApiMessage(null);
    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    const currentTable = table; // Capture table instance
    const selectedRows = currentTable.getSelectedRowModel().rows.map(row => row.original);


    for (const school of selectedRows) {
        try {
            const response = await handleDeleteSchoolAction(school.id);
            if (response.status === 'success') {
            successCount++;
            } else {
            errorCount++;
            errors.push(formatApiMessage(response.message) || `Failed to delete ${entityName} ${school.name}`);
            }
        } catch (error: any) {
            errorCount++;
            errors.push(formatApiMessage(error.message) || `Error deleting ${entityName} ${school.name}`);
        }
    }

    setInternalLoading(false);
    let message = '';
    if (successCount > 0) message += `${successCount} ${successCount === 1 ? entityName : entityNamePlural} deleted successfully. `;
    if (errorCount > 0) message += `${errorCount} ${errorCount === 1 ? entityName : entityNamePlural} failed to delete. ${errors.join('; ')}`;
    
    setApiMessage({ type: errorCount > 0 ? 'error' : 'success', message });
    setRowSelection({}); // Clear selection
    onRefresh();
  };

  const columns = useMemo<ColumnDef<ISchoolResponse>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllPageRowsSelected()}
            indeterminate={table.getIsSomePageRowsSelected()}
            onChange={table.getToggleAllPageRowsSelectedHandler()}
            aria-label={`Select all ${entityNamePlural} on this page`}
          />
        ),
        cell: ({ row }: { row: Row<ISchoolResponse>}) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
            aria-label={`Select ${entityName} ${row.original.name}`}
          />
        ),
        size: 40,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')} className="px-2 py-1">
            School Name
            {column.getIsSorted() === 'asc' && <ChevronUp className="ml-2 h-4 w-4" />}
            {column.getIsSorted() === 'desc' && <ChevronDown className="ml-2 h-4 w-4" />}
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
              <SchoolIcon size={20} />
            </div>
            <div>
              <div className="font-medium text-gray-800 block text-sm">{row.original.name}</div>
              <div className="text-xs text-gray-500 mt-0.5">ID: {row.original.id.substring(0,8)}...</div>
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')} className="px-2 py-1">
            Email
            {column.getIsSorted() === 'asc' && <ChevronUp className="ml-2 h-4 w-4" />}
            {column.getIsSorted() === 'desc' && <ChevronDown className="ml-2 h-4 w-4" />}
          </Button>
        ),
        cell: ({ row }) => <div className="text-sm text-gray-700">{row.original.email || 'N/A'}</div>,
      },
      {
        accessorKey: 'phoneNumber',
        header: 'Phone',
        cell: ({ row }) => <div className="text-sm text-gray-700">{row.original.phoneNumber || 'N/A'}</div>,
      },
      {
        id: 'actions',
        header: () => <div className="text-right pr-2">Actions</div>,
        cell: ({ row }) => (
          <div className="flex items-center justify-end gap-0.5">
            <Button
              variant="ghost"
              onClick={() => handleViewSchool(row.original.id)}
              className="p-1.5 h-8 w-8"
              title={`View ${entityName} Details`}
            >
              <Eye size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => onEditSchool(row.original)} // Call the new prop
              className="p-1.5 h-8 w-8"
              title={`Edit ${entityName}`}
            >
              <Edit size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleOpenDeleteModal(row.original)}
              className="text-red-600 hover:bg-red-50 hover:text-red-700 p-1.5 h-8 w-8"
              title={`Delete ${entityName}`}
            >
              <Trash2 size={16} />
            </Button>
          </div>
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [entityName, entityNamePlural] // Dependencies for column memoization
  );

  const table = useReactTable({
    data: locallyFilteredSchools,
    columns,
    state: {
      sorting,
      pagination,
      rowSelection,
      globalFilter,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: false, // Let TanStack Table handle pagination of locallyFilteredSchools
    manualSorting: false,   // Let TanStack Table handle sorting
    manualFiltering: false, // Let TanStack Table handle global filtering
    enableRowSelection: true,
  });

  const hasActiveFilters = searchTerm !== ''; // Now only based on search term
  const activeFiltersCount = searchTerm ? 1 : 0; // Now only based on search term
  
  const paginatedData = table.getRowModel().rows.map(row => row.original); // Data for current page

  return (
    <div>
      <style dangerouslySetInnerHTML={{ __html: AnimationStyles }} />
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <SchoolTableHeader
          title={tableTitle}
          subtitle={isLoading ? `Loading ${entityNamePlural}...` : `${table.getFilteredRowModel().rows.length} ${table.getFilteredRowModel().rows.length === 1 ? entityName : entityNamePlural} found`}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          onClearSearch={clearSearch}
          // Filter-related props are fully removed from SchoolTableHeader call
          hasActiveFilters={hasActiveFilters} 
          activeFiltersCount={activeFiltersCount} 
          // Create button prop is fully removed from SchoolTableHeader call
        />

        {/* Filter panel rendering is completely removed */}

        {table.getSelectedRowModel().rows.length > 0 && (
          <SchoolTableBulkActions
            selectedCount={table.getSelectedRowModel().rows.length}
            onClearSelection={() => table.resetRowSelection()}
            onBulkDelete={handleBulkDelete}
            entityName={entityName}
          />
        )}
        
        {apiMessage && (
          <div className="my-4 px-4 md:px-6">
            <AlertMessage type={apiMessage.type as AlertMessageProps['type']} message={apiMessage.message} />
          </div>
        )}

        <div className="overflow-x-auto scrollbar max-h-[calc(100svh-280px)]">
          <CustomTable<ISchoolResponse>
            columns={columns}
            tableData={paginatedData} // Data for the current page
            isLoading={isLoading && paginatedData.length === 0}
            manualPagination={true} // Since we are controlling pagination via useReactTable state
            pageCount={table.getPageCount()}
            pagination={table.getState().pagination}
            onPaginationChange={table.setPagination}
            // Pass other necessary props like sorting if CustomTable handles them
            // For now, CustomTable seems to handle selection internally.
            // noDataContent prop is not directly supported by CustomTable in the way it was used.
            // CustomTable has its own "Ooops! Looks like there is no data" or loading state.
            // We can enhance CustomTable or handle complex empty states outside if needed.
          />
        </div>
        
        {/* Pagination component, driven by the table state */}
        {!isLoading && table.getFilteredRowModel().rows.length > 0 && table.getPageCount() > 0 && (
          <TablePagination
            currentPage={table.getState().pagination.pageIndex + 1}
            totalPages={table.getPageCount()}
            rowsPerPage={table.getState().pagination.pageSize}
            totalItems={table.getFilteredRowModel().rows.length} // Total items after filtering
            onPageChange={(page) => table.setPageIndex(page - 1)}
            onRowsPerPageChange={(newPageSize) => table.setPageSize(Number(newPageSize.target.value))}
          />
        )}
      </div>
      <DeleteSchoolModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        school={selectedSchoolForModal}
        onSuccess={() => {
          handleDeleteSchool(); // Call existing delete handler which refreshes
        }}
      />
    </div>
  );
};
