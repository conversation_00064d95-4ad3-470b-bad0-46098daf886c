"use client"
import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';

const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL;
const DEFAULT_TIMEOUT = 10000; // 10 seconds

let socket: Socket | null = null;
const pendingOperations = new Map<string, (data: any) => void>();

export const getSocket = (): Socket => {
  if (!socket) {
    socket = io(SOCKET_URL, {
      transports: ['websocket'], // Use WebSocket transport
      reconnection: true, // Enable reconnection
      reconnectionAttempts: 5, // Number of reconnection attempts
      timeout: DEFAULT_TIMEOUT, // Connection timeout
    });

    socket.on('connect', () => {
      console.log('Socket connected:', socket?.id);
      // Request sync on connect to ensure UI is up-to-date
      socket?.emit('sync_request', { component: 'worksheet' });
    });

    socket.on('disconnect', () => {
      console.log('Socket disconnected');
    });

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
    });

    // Set up worksheet handlers
    setupWorksheetHandlers(socket);
  }

  return socket;
};

/**
 * Track an operation with a timeout
 * @param operationId Unique ID for the operation
 * @param callback Function to call when operation completes or times out
 * @param timeout Optional timeout in milliseconds (defaults to DEFAULT_TIMEOUT)
 * @returns The operation ID for reference
 */
export const trackOperation = (
  operationId: string = uuidv4(),
  callback: (data: any) => void,
  timeout: number = DEFAULT_TIMEOUT
): string => {
  // Store the callback in the pending operations map
  pendingOperations.set(operationId, callback);

  // Set a timeout to remove the operation and call the callback with an error
  setTimeout(() => {
    if (pendingOperations.has(operationId)) {
      const cb = pendingOperations.get(operationId);
      pendingOperations.delete(operationId);
      if (cb) {
        cb({ error: 'Operation timed out' });
      }
    }
  }, timeout);

  return operationId;
};

/**
 * Set up event handlers for worksheet-related events
 * @param socket The socket instance
 */
export const setupWorksheetHandlers = (socket: Socket): void => {
  // Listen for worksheet status updates
  socket.on('worksheet_status_update', (data: { 
    operationId?: string;
    status: string;
    result: any;
  }) => {
    console.log('Received worksheet_status_update:', data);

    // If there's an operationId, check if we have a pending operation
    if (data.operationId && pendingOperations.has(data.operationId)) {
      const callback = pendingOperations.get(data.operationId);
      pendingOperations.delete(data.operationId);

      if (callback) {
        callback({
          status: data.status,
          result: data.result
        });
      }
    }

    // Dispatch a global custom event for real-time updates across views
    const event = new CustomEvent('worksheet_updated', { detail: data });
    window.dispatchEvent(event);
  });
};
