import { z } from 'zod';

/**
 * Zod validation schema for school creation form
 * Aligns with ICreateSchoolPayload interface and task requirements
 */
export const createSchoolSchema = z.object({
  // Required field with specific length constraints and error messages
  name: z
    .string()
    .min(3, { message: 'School name must be at least 3 characters.' })
    .max(100, { message: 'School name must be 100 characters or less.' }),
  
  // Optional field - can be empty string or valid address
  address: z
    .string()
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid phone number in international format
  phoneNumber: z
    .string()
    .regex(/^\+?[1-9]\d{1,14}$/, {
      message: 'Invalid phone number format. Example: +12223334444'
    })
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid registered number
  registeredNumber: z
    .string()
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid email format
  email: z
    .string()
    .email({ message: 'Invalid email address.' })
    .optional()
    .or(z.literal('')),
  
  // Optional UUID field for admin assignment
  adminId: z
    .string()
    .uuid({ message: 'Invalid admin ID format.' })
    .optional()
    .or(z.literal('')),
  
  // Optional UUID field for brand assignment
  brandId: z
    .string()
    .uuid({ message: 'Invalid brand ID format.' })
    .optional()
    .or(z.literal(''))
});

/**
 * Zod validation schema for school update form
 * Similar to createSchoolSchema but with different optionality for update context
 * Aligns with IUpdateSchoolPayload interface
 */
export const updateSchoolSchema = z.object({
  // Optional field - when provided, must meet same constraints as creation
  name: z
    .string()
    .min(3, { message: 'School name must be at least 3 characters.' })
    .max(100, { message: 'School name must be 100 characters or less.' })
    .optional(),
  
  // Optional field - can be empty string or valid address
  address: z
    .string()
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid phone number in international format
  phoneNumber: z
    .string()
    .regex(/^\+?[1-9]\d{1,14}$/, {
      message: 'Invalid phone number format. Example: +12223334444'
    })
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid registered number
  registeredNumber: z
    .string()
    .optional()
    .or(z.literal('')),
  
  // Optional field - can be empty string or valid email format
  email: z
    .string()
    .email({ message: 'Invalid email address.' })
    .optional()
    .or(z.literal('')),
  
  // Optional UUID field for admin assignment (typically not changed in updates)
  adminId: z
    .string()
    .uuid({ message: 'Invalid admin ID format.' })
    .optional()
    .or(z.literal('')),
  
  // Optional UUID field for brand assignment
  brandId: z
    .string()
    .uuid({ message: 'Invalid brand ID format.' })
    .optional()
    .or(z.literal(''))
});

// Export TypeScript types inferred from the schemas
export type CreateSchoolFormData = z.infer<typeof createSchoolSchema>;
export type UpdateSchoolFormData = z.infer<typeof updateSchoolSchema>;

// Export refined schemas for React Hook Form integration
export const createSchoolFormSchema = createSchoolSchema;
export const updateSchoolFormSchema = updateSchoolSchema;
