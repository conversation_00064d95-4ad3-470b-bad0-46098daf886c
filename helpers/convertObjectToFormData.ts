export const convertObjectToFormData = (values: Record<string, any>) => {

  if (!values || Object.keys(values).length === 0) {
    console.warn('Warning: Empty or undefined values passed to convertObjectToFormData');
    return {}; // Return empty object with a warning
  }

  // UUID regex pattern to identify random fields
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  // Create a clean copy of the values with the structure you want
  const cleanedValues: Record<string, any> = {};

  // Store item labels for lookup when converting item IDs to objects
  const itemLabelsMap: Record<string, string> = {};

  // First pass: collect all item labels from any nested structures
  Object.entries(values).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(item => {
        if (item && typeof item === 'object' && item.label && item.id) {
          // Store label by ID for lookup
          itemLabelsMap[item.id] = item.label;
        }

        // Look in deeper nesting for item labels
        if (item && typeof item === 'object' && item.items && Array.isArray(item.items)) {
          item.items.forEach((subItem: any) => {
            if (subItem && typeof subItem === 'object' && subItem.id) {
              itemLabelsMap[subItem.id] = subItem.label || '';
            } else if (typeof subItem === 'string') {
              // Look for labels in options or other places
              // We'll populate this in the second pass
            }
          });
        }
      });
    }
  });

  // Second pass: transform the data structure
  Object.entries(values).forEach(([key, value]) => {
    // Skip null or undefined values
    if (value === null || value === undefined) {
      return;
    }

    // Skip fields with UUID keys that are not part of the expected structure
    if (uuidPattern.test(key)) {
      return; // Skip this field
    }

    // Handle different types of values
    if (Array.isArray(value)) {
      if (key === 'subject' && value.length > 0 && typeof value[0] === 'object' && value[0] !== null) {
        // Format subject array as specified:
        // [{ label: "Speed", items: ["Speed Conversion", ...] }, ...]
        cleanedValues[key] = value.map(item => {
          // Get the label from the item
          const label = item.label || '';

          // Process items array if it exists
          const items: string[] = [];
          if (item.items && Array.isArray(item.items)) {
            item.items.forEach((subItem: any) => {
              if (typeof subItem === 'object' && subItem !== null) {
                // If it's an object with label, use that
                if (subItem.label) {
                  items.push(subItem.label);
                } else if (subItem.id && itemLabelsMap[subItem.id]) {
                  // If it has id and we have a mapping, use that
                  items.push(itemLabelsMap[subItem.id]);
                }
              } else if (typeof subItem === 'string') {
                // If it's a string ID, try to find its label
                if (itemLabelsMap[subItem]) {
                  items.push(itemLabelsMap[subItem]);
                } else {
                  // No mapping found, just use the string as is
                  items.push(subItem);
                }
              }
            });
          }

          return {
            label,
            items
          };
        });
      } else if (key === 'question_type') {
        // For question_type array, only keep label and count properties
        cleanedValues[key] = value.map(item => ({
          label: item.label,
          count: item.count
        }));
      } else {
        // For other arrays
        cleanedValues[key] = [...value]; // Create a clean copy of the array
      }
    } else if (typeof value === 'object' && value !== null) {
      if ('value' in value && 'label' in value) {
        // For grade, topic, level, language, question_count objects
        // Just use the label value as a string
        cleanedValues[key] = value.label;
      } else {
        // For other objects, create a clean copy
        cleanedValues[key] = { ...value };
      }
    } else {
      // For primitive values
      cleanedValues[key] = value;
    }
  });

  return cleanedValues;
};