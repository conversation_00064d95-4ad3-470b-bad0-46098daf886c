import { NextResponse } from 'next/server';

export function middleware(req: Request) {
  const response = NextResponse.next();

  // Set the custom header with the current path
  response.headers.set('x-current-path', new URL(req.url).pathname);

  // Log for debugging
  // console.log('Path:', new URL(req.url).pathname);

  return response;
}

export const config = {
  // Matcher for all routes
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|assets|public).*)'],
};
